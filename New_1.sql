/**
drop table ads.ads_bi_ad_short_cost_info_1d;
CREATE TABLE ads.ads_bi_ad_short_cost_info_1d(
   `short_play_code` string COMMENT '剧id'
  ,`app_language` varchar(65533) COMMENT '界面语言'
  ,`short_play_name` varchar(65533) COMMENT '剧名'
  ,`short_play_name_alias` varchar(65533) COMMENT '别名'
  ,`short_play_type` string COMMENT '短剧类型 1-自制剧 2-解说剧 3-配音翻译剧 ,4-字幕翻译剧，5-版权剧,6-测试剧，7-AI翻译剧，8-自制翻译剧 '
  ,`language_name` string COMMENT '语言'
  ,`pub_time` string COMMENT '上线时间'
  ,`cost_amt` decimal(20, 6) COMMENT '投放消耗($)'
  ,`viewers_unt` bigint COMMENT '播放人数'
  ,`short_play_one_code` string COMMENT '最多流向剧code'
  ,`viewers_unt_one` bigint COMMENT '最多流向用户数'
  ,`short_play_two_code` string COMMENT '第二流向剧code'
  ,`viewers_unt_two` bigint COMMENT '第二流向用户数'
  ,`short_play_three_code` string COMMENT '第三流向剧code'
  ,`viewers_unt_three` bigint COMMENT '第三流向用户数'
  ,`etl_tm` string  COMMENT "清洗时间"
 )
COMMENT '短剧流转情况分析'
PARTITIONED BY (
  `dt` string COMMENT '事件事件' );
*/

drop table ads.ads_bi_ad_short_cost_info_1d;
CREATE TABLE ads.ads_bi_ad_short_cost_info_1d
(
   `dt` date NOT NULL COMMENT "日期"
  ,`short_play_code` varchar(65533)  COMMENT '剧id'
  ,`app_language` varchar(65533) COMMENT '界面语言'
  ,`short_play_name` varchar(65533) COMMENT '剧名'
  ,`short_play_name_alias` varchar(65533) COMMENT '别名'
  ,`short_play_type` int COMMENT '短剧类型 1-自制剧 2-解说剧 3-配音翻译剧 ,4-字幕翻译剧，5-版权剧,6-测试剧，7-AI翻译剧，8-自制翻译剧 '
  ,`language_name` varchar(65533) COMMENT '语言'
  ,`pub_time` varchar(65533) COMMENT '上线时间'
  ,`cost_amt` decimal(20, 6) COMMENT '投放消耗($)'
  ,`viewers_unt` bigint COMMENT '播放人数'
  ,`short_play_one_code` string COMMENT '最多流向剧code'
  ,`viewers_unt_one` bigint COMMENT '最多流向用户数'
  ,`short_play_two_code` string COMMENT '第二流向剧code'
  ,`viewers_unt_two` bigint COMMENT '第二流向用户数'
  ,`short_play_three_code` string COMMENT '第三流向剧code'
  ,`viewers_unt_three` bigint COMMENT '第三流向用户数'
  ,`etl_tm` string  COMMENT "清洗时间"
 )ENGINE=OLAP
PRIMARY KEY(`dt`, `short_play_code`, `language_code`)
COMMENT '短剧流转情况分析'
PARTITION BY date_trunc('day', dt)
DISTRIBUTED BY HASH(`short_play_code`) BUCKETS 1
PROPERTIES 
(
"compression" = "ZSTD",
"enable_persistent_index" = "true",
"fast_schema_evolution" = "true",
"replicated_storage" = "true",
"replication_num" = "3",
"storage_medium" = "SSD"
);

-- 88

CREATE TABLE ads.ads_bi_ad_short_cost_info_1d
(
    `dt` DATE NOT NULL COMMENT "日期",
    `short_play_code` VARCHAR(65533) COMMENT '剧id',
    `app_language` VARCHAR(65533) COMMENT '界面语言',
    `short_play_name` VARCHAR(65533) COMMENT '剧名',
    `short_play_name_alias` VARCHAR(65533) COMMENT '别名',
    `short_play_type` INT COMMENT '短剧类型 1-自制剧 2-解说剧 3-配音翻译剧 ,4-字幕翻译剧，5-版权剧,6-测试剧，7-AI翻译剧，8-自制翻译剧',
    `language_name` VARCHAR(65533) COMMENT '语言',
    `pub_time` VARCHAR(65533) COMMENT '上线时间',
    `cost_amt` DECIMAL(20, 6) COMMENT '投放消耗($)',
    `viewers_unt` BIGINT COMMENT '播放人数',
    `short_play_one_code` VARCHAR(65533) COMMENT '最多流向剧code',
    `viewers_unt_one` BIGINT COMMENT '最多流向用户数',
    `short_play_two_code` VARCHAR(65533) COMMENT '第二流向剧code',
    `viewers_unt_two` BIGINT COMMENT '第二流向用户数',
    `short_play_three_code` VARCHAR(65533) COMMENT '第三流向剧code',
    `viewers_unt_three` BIGINT COMMENT '第三流向用户数',
    `etl_tm` VARCHAR(65533) COMMENT "清洗时间"
)
PRIMARY KEY(`dt`, `short_play_code`, `app_language`)
COMMENT '短剧流转情况分析'
PARTITION BY DATE_TRUNC('day', dt)
DISTRIBUTED BY HASH(`short_play_code`) BUCKETS 1
PROPERTIES 
(
    "compression" = "ZSTD",
    "enable_persistent_index" = "true",
    "fast_schema_evolution" = "true",
    "replicated_storage" = "true",
    "replication_num" = "3",
    "storage_medium" = "SSD"
);


/*CREATE TABLE ads.ads_bi_short_play_exposure_3d (
  `dt` date NOT NULL COMMENT "日期",
  `short_play_id` int(11) NOT NULL COMMENT "自增的主键id，物理意义上的short_play_id ",
  `language_code` varchar(65533) NOT NULL COMMENT "语言",
  `short_play_code` varchar(65533) NULL COMMENT "短剧id",
  `short_play_name` varchar(65533) NULL COMMENT "短剧名称",
  `short_play_type` string NULL COMMENT "短剧类型 1-自制剧 2-解说剧 3-翻译剧 ,4-字幕剧，5-版权剧，6-测试剧",
  `total_episodes` int(11) NULL COMMENT "总集数",
  `pub_time` datetime NULL COMMENT "上线时间",
  `reel_show_cnt` int(11) NULL COMMENT "3日该剧展示用户数和3日交活用户数交集去重",
  `interactive_cnt` int(11) NULL COMMENT "3日交活用户数去重",
  `rate` double NULL COMMENT "三日日活覆盖率%",
  `reel_show_cnt7` int(11) NULL COMMENT "3日该剧展示用户数和7日交活用户数交集去重",
  `interactive_cnt7` int(11) NULL COMMENT "7日交活用户数去重",
  `rate7` double NULL COMMENT "三日日活覆盖率%",
  `etl_tm` datetime NULL COMMENT "清洗时间"
) ENGINE=OLAP
PRIMARY KEY(`dt`, `short_play_id`, `language_code`)
COMMENT "短剧曝光信息表"
DISTRIBUTED BY HASH(`short_play_id`) BUCKETS 1
PROPERTIES (
"compression" = "ZSTD",
"enable_persistent_index" = "true",
"fast_schema_evolution" = "true",
"replicated_storage" = "true",
"replication_num" = "3",
"storage_medium" = "SSD"
);*/  
  
/*CREATE TABLE ads.ads_bi_short_play_recharge (
    `dt` date not null comment "日期",
    `short_play_id` int NOT NULL COMMENT "自增的主键id，物理意义上的short_play_id ",
    `language_code` string not NULL COMMENT "语言",
    `country_code` string not NULL COMMENT "国家编码",
    `short_play_code` string NULL COMMENT "短剧编码，长码",
    `amount` double NULL COMMENT "充值金额",
    `id` bigint NOT NULL AUTO_INCREMENT COMMENT "自增id",
    `etl_tm` datetime NULL COMMENT "清洗时间"
) ENGINE=OLAP
PRIMARY KEY(dt,`short_play_id`,language_code,country_code)
COMMENT "剧集排名表"
DISTRIBUTED BY HASH(`short_play_id`) BUCKETS 1
PROPERTIES (
"compression" = "ZSTD",
"fast_schema_evolution" = "true",
"replicated_storage" = "true",
"storage_medium" = "SSD"
);*/  
  
drop table ads.ads_bi_ad_short_cost_info_1d;
CREATE TABLE ads.ads_bi_ad_short_cost_info_1d (
  `dt` date NOT NULL COMMENT "日期",
  `short_play_code` varchar(65533) NOT NULL COMMENT "剧code",
  `app_language` varchar(65533) NOT NULL COMMENT "app语言",
  `short_play_name` varchar(65533) NULL COMMENT "剧名",
  `short_play_name_alias` varchar(65533) NULL COMMENT "别名",
  `short_play_type` int(11) NULL COMMENT "短剧类型 1-自制剧 2-解说剧 3-配音翻译剧 ,4-字幕翻译剧，5-版权剧,6-测试剧，7-AI翻译剧，8-自制翻译剧 ",
  `language_name` varchar(65533) NULL COMMENT "语言",
  `pub_time` varchar(65533) NULL COMMENT "上线时间",
  `cost_amt` decimal(20, 6) NULL COMMENT "投放消耗($)",
  `viewers_unt` bigint(20) NULL COMMENT "播放人数",
  `short_play_one_code` varchar(65533) NULL COMMENT "最多流向剧code",
  `viewers_unt_one` bigint(20) NULL COMMENT "最多流向用户数",
  `short_play_two_code` varchar(65533) NULL COMMENT "第二流向剧code",
  `viewers_unt_two` bigint(20) NULL COMMENT "第二流向用户数",
  `short_play_three_code` varchar(65533) NULL COMMENT "第三流向剧code",
  `viewers_unt_three` bigint(20) NULL COMMENT "第三流向用户数",
  `etl_tm` varchar(65533) NULL COMMENT "清洗时间",
  `dt` date NOT NULL COMMENT "日期(事件时间）",
) ENGINE=OLAP 
PRIMARY KEY(`dt`, `short_play_code`, `app_language`)
COMMENT "短剧流转情况分析"
PARTITION BY date_trunc('day', dt)
DISTRIBUTED BY HASH(`dt`, `short_play_code`, `app_language`) BUCKETS 1 
PROPERTIES (
"compression" = "ZSTD",
"enable_persistent_index" = "false",
"fast_schema_evolution" = "true",
"replicated_storage" = "true",
"replication_num" = "3"
);


echo "===============start node  ${system.task.definition.name}========================="

biz_date=${date_var}
task_name=${system.task.definition.name}
database=${task_name%.*}
table=${task_name#*.}

echo $database
echo $table

set -e
python3 /data/dataware/batchdatawarehouse/python/public_run_sql.py $table ${date_var}

echo "===============end node  ${system.task.definition.name}========================="




with user_short_three_info_tmp as (   -- 需要当天播放数据
    -- 获取近3天观剧详情，且播放时长>=5秒
    select
      dt,
      user_id,
      app_language,
      short_play_code  as short_play_code
    from dwm.dwm_watch_user_play_time_real_1d -- 观看主表
    where dt >= date_add('2025-08-07', -2) and dt <= '2025-08-07'
      and eff_watch_cnt > 0 -- 有效观看
      and scene = 'immersion' -- 沉浸页
    group by dt, user_id, app_language, short_play_code
  ),
  total_short_viewers_tmp as ( -- 当天播放每个剧的有效观看人数
    select
      dt,
      short_play_code,-- 剧code
      app_language,-- app语言
      count(distinct user_id) as total_viewers_unt -- 观看用户数
    from user_short_three_info_tmp
    group by dt, short_play_code, app_language
  ),
short_flow_code_tmp as ( 
-- 构建流向关系：用户看过的剧 A ,同一用户看过的剧 B（A不等于B）
select
t.dt,
t.short_play_code,
t.app_language,
p.user_id,
p.short_play_code as flow_code
from user_short_three_info_tmp t
inner join dwm.dwm_watch_user_play_time_real_1d p
on p.user_id = t.user_id -- 同一用户
where p.dt between date_add('2025-08-07', -2) and date_add('2025-08-07', 2) -- 前两天到后两天之间
and p.dt between t.dt and date_add(t.dt, 2)
and p.eff_watch_cnt > 0 -- 有效观看
and scene = 'immersion' -- 沉浸页
and p.short_play_code <> t.short_play_code
  ),
  
flow_code_user_unt as (  -- 统计流向Top3
  select
    dt,
    short_play_code as short_play_code, 
    app_language, -- app语言
    max(case when rn = 1 then flow_code end) as short_play_one_code, -- 最多流向剧code
    max(case when rn = 2 then flow_code end) as short_play_two_code, -- 第二流向剧code
    max(case when rn = 3 then flow_code end) as short_play_three_code, -- 第三流向剧code
    sum(case when rn = 1 then viewers_unt end) as viewers_unt_one, -- 最多流向用户数
    sum(case when rn = 2 then viewers_unt end) as viewers_unt_two, -- 第二流向用户数
    sum(case when rn = 3 then viewers_unt end) as viewers_unt_three -- 第三流向用户数
  from (
    select
      dt,
      short_play_code,
      app_language,
      flow_code, -- 流向code
      count(distinct user_id) as viewers_unt,
      row_number() over (partition by dt, short_play_code, app_language order by count(distinct user_id) desc) as rn
    from short_flow_code_tmp -- 流向关系表
    group by dt, short_play_code, app_language, flow_code
  ) tt
  where rn <= 3
  group by dt, short_play_code, app_language
),  
 
  ad_cost_amt_tmp as (  -- 需要严格限制当天数据
    -- 广告投放消耗
    select
      dt,
      short_play_code,
      app_language,
      sum(cost_amt) as cost_amt
    from dwm.dwm_ad_advert_place_cost_1d
    where dt >= date_add('2025-08-07', -2) and dt <= '2025-08-07'
      and short_play_id is not null
    group by dt, short_play_code, app_language
  )
,
-- 主查询：关联数据并插入目标表
delete  from  ads.ads_bi_ad_short_cost_info_1d where dt>=date_add('2025-08-07',-2) and dt<='2025-08-07';
insert overwrite table ads.ads_bi_ad_short_cost_info_1d partition (dt='2025-08-07')
select
  a.short_play_code as short_play_code,
  a.app_language,
  b.short_play_name,
  b.short_play_name_alias,
  b.short_play_type,
  cast(b.language_name as string) as language_name,
  b.pub_time,
  a.cost_amt as cost_amt,
  a.viewers_unt,
  a.short_play_one_code,
  a.viewers_unt_one,
  a.short_play_two_code,
  a.viewers_unt_two,
  a.short_play_three_code,
  a.viewers_unt_three,
  a.etl_tm,
  a.dt
from (
  select
    coalesce(t.short_play_code, t1.short_play_code, t2.short_play_code, '') as short_play_code,
    coalesce(t.app_language, t1.app_language, t2.app_language, '') as app_language,
    coalesce(tt.dt, t.dt, t1.dt, t2.dt) as dt,
    sum(nvl(t2.cost_amt, 0)) as cost_amt,
    sum(nvl(t.total_viewers_unt, 0)) as viewers_unt,
    max(t1.short_play_one_code) as short_play_one_code,
    sum(nvl(t1.viewers_unt_one, 0)) as viewers_unt_one,
    max(t1.short_play_two_code) as short_play_two_code,
    sum(nvl(t1.viewers_unt_two, 0)) as viewers_unt_two,
    max(t1.short_play_three_code) as short_play_three_code,
    sum(nvl(t1.viewers_unt_three, 0)) as viewers_unt_three,
    current_timestamp() as etl_tm
  from (
    -- 每天全量剧集+日期组合
    select date('2025-08-07') as dt, short_play_code as short_play_code from dim.dim_short_play_id_info 
    union all
    select date(date_add('2025-08-07', -1)) as dt, short_play_code as short_play_code from dim.dim_short_play_id_info 
    union all
    select date(date_add('2025-08-07', -2)) as dt, short_play_code as short_play_code from dim.dim_short_play_id_info  
  ) tt 
  -- 关联观看数据
  full join total_short_viewers_tmp t on tt.short_play_code = t.short_play_code and tt.dt = t.dt
  -- 关联流向数据
  full join flow_code_user_unt t1 on tt.short_play_code = t1.short_play_code and t.app_language = t1.app_language and tt.dt = t1.dt
  -- 关联广告消耗数据
  full join ad_cost_amt_tmp t2 on tt.short_play_code = t2.short_play_code and t.app_language = t2.app_language and tt.dt = t2.dt
  group by
    coalesce(t.short_play_code, t1.short_play_code, t2.short_play_code, ''),
    coalesce(t.app_language, t1.app_language, t2.app_language, ''),
    coalesce(tt.dt, t.dt, t1.dt, t2.dt)
) a
left join dim.dim_short_play_id_info b on a.short_play_code = b.short_play_code
where trim(a.short_play_code) <> '';




数据验证：
select dt,sum(cost_amt),sum(viewers_unt) from ads.ads_bi_ad_short_cost_info_1d group by dt;
2025-08-05      300941.8019730003 293440
2025-08-06      290735.458949     295595
2025-08-07      287555.391691     290775
-- 人数
select dt, sum(user_id) as use_cnt
from (
  select
    dt,
    app_language,
    short_play_code as short_play_code,
    count(distinct user_id) as user_id  
  from dwm.dwm_watch_user_play_time_real_1d -- 观看主表
  where dt >= date_add('2025-08-07', -2) 
    and dt <= '2025-08-07'  -- 修正逻辑运算符（d→and）
    and eff_watch_cnt > 0 -- 有效观看
    and scene = 'immersion' -- 沉浸页
  group by dt, app_language, short_play_code
) t 
group by dt;
2025-08-05   293440
2025-08-06   295595
2025-08-07   290775

-- 金额
 select
      dt,
      sum(cost_amt) as cost_amt
    from dwm.dwm_ad_advert_place_cost_1d
    where dt >= date_add('2025-08-07', -2) and dt <= '2025-08-07'
      and short_play_id is not null
    group by dt;
2025-08-05   300941.801973
2025-08-06   290735.458949
2025-08-07   287555.391691


备份
 select dt, app_language, cast(short_play_code as string), count(distinct user_id) as total_viewers_unt 
    from dwm.dwm_watch_user_play_time_real_1d 
    where dt >= date_add('2025-08-07', -2) and dt <= '2025-08-07'
      and eff_watch_cnt > 0 -- 有效观看
      and scene = 'immersion' -- 沉浸页
    group by dt, app_language, short_play_code
	

shell:

echo "===============start node ${system.task.definition.name}========================="

# 定义重跑日期范围
start_date="2025-04-19"
end_date="2025-08-06"

task_name=${system.task.definition.name}
database=${task_name%.*}
table=${task_name#*.}

echo "Database: $database"
echo "Table: $table"

# 循环处理日期范围
current_date="$start_date"
while [ "$current_date" != "$end_date" ]; do
    # 执行数据处理
    echo "Processing date: $current_date"
    python3 /data/dataware/batchdatawarehouse/python/public_run_sql.py $table $current_date
    
    # 移动到下一天
    current_date=$(date -d "$current_date + 1 day" +"%Y-%m-%d")
done

# 处理结束日期（包含最后一天）
echo "Processing date: $end_date"
python3 /data/dataware/batchdatawarehouse/python/public_run_sql.py $table $end_date

echo "===============end node ${system.task.definition.name}========================="