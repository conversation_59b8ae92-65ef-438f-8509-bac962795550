import sys
from datetime import datetime,timezone, timedelta, date 
from public_init import *
from public_fun import *
from public_logging import *
from public_task_table import *

# 设置参数 传参  参数1：库名 参数2：表名 参数3：执行日期yyyy-MM-dd
argv_list =  sys.argv
db_name = None
table_name = None
time_s = None

if len(argv_list) == 2:
    #db_name = argv_list[1]
    table_name = argv_list[1]
    time_s = (datetime.now(timezone.utc) - timedelta(hours=16) ).strftime('%Y-%m-%d')
elif len(argv_list) == 3:
    #db_name = argv_list[1]
    table_name = argv_list[1]
    time_s = argv_list[2]
else:
    print("参数个数错误，程序退出")
    exit()

#初始化log
initlog(table_name)

db_name = str(table_name).split('_')[0]

logger.info(f"入参：{db_name} {table_name} {time_s}")

#运行分区控制
time_now = (datetime.now(timezone.utc) + timedelta(hours=8) ).strftime('%Y%m%d')
logger.info(f"当前时间: {time_now}")

if int(time_now.replace('-','')) < int(time_s.replace('-','')):
    logger.info("禁止执行大于当天时间的分区")
    exit(1)
#运行分区控制

ini_filename = f"{CLUSTER_PATH}/config/{db_name}/{table_name}.ini"
if os.path.isfile(ini_filename):
    logger.info(f"任务配置文件存在:{ini_filename}")
else:
    logger.error(f"任务配置文件不存在:{ini_filename}")
    exit()

sql_type = read_config(ini_filename,"sql_type")
date_var = read_config(ini_filename,"date_var")
hive_db = read_config(ini_filename,"hive_db")
hive_db=hive_db.replace('\"', '')
table_name = read_config(ini_filename,"table_name")
table_name=table_name.replace('\"', '')
queue = read_config(ini_filename,"queue")
executor_memory = read_config(ini_filename,"executor_memory")
driver_memory = read_config(ini_filename,"driver_memory")
num_executors = read_config(ini_filename,"num_executors")
executor_cores = read_config(ini_filename,"executor_cores")
other_conf = read_config(ini_filename,"other_conf")
other_conf_hive = read_config(ini_filename,"other_conf_hive")
other_conf_spark = read_config(ini_filename,"other_conf_spark")

starrocks_output=read_config(ini_filename,"starrocks_output")
starrocks_output_withpartition=read_config(ini_filename,"starrocks_output_withpartition")
starrocks_queue=read_config(ini_filename,"starrocks_queue")
starrocks_executor_memory=read_config(ini_filename,"starrocks_executor_memory")
starrocks_driver_memory=read_config(ini_filename,"starrocks_driver_memory")
starrocks_num_executors=read_config(ini_filename,"starrocks_num_executors")
starrocks_executor_cores=read_config(ini_filename,"starrocks_executor_cores")
starrocks_sink_columntypes=read_config(ini_filename,"starrocks_sink_columntypes")
starrocks_sink_transform_map=read_config(ini_filename,"starrocks_sink_transform_map")

if executor_memory is None:
    executor_memory = "5g"
if driver_memory is None:
    driver_memory = "8g"
if num_executors is None:
    num_executors = "12"
if executor_cores is None:
    executor_cores = "1"
if other_conf is None:
    other_conf = ""
if other_conf_hive is None:
    other_conf_hive = ""
if other_conf_spark is None:
    other_conf_spark = ""

if starrocks_output is None:
    starrocks_output = "0"
if starrocks_output_withpartition is None:
    starrocks_output_withpartition="1"
if starrocks_queue is None:
    starrocks_queue="auto"
if starrocks_executor_memory is None:
    starrocks_executor_memory = "5g"
if starrocks_driver_memory is None:
    starrocks_driver_memory = "8g"
if starrocks_num_executors is None:
    starrocks_num_executors = "12"
if starrocks_executor_cores is None:
    starrocks_executor_cores = "1"
if starrocks_sink_columntypes is None:
    starrocks_sink_columntypes = ""
if starrocks_sink_transform_map is None:
    starrocks_sink_transform_map = ""

if sql_type is None:
    sql_type = "sparkcluster"

spark_path = f"{CLUSTER_PATH}/spark/{db_name}/{table_name}.sql"

if os.path.isfile(spark_path):
    logger.info(f"SQL 文件存在:{spark_path}")
else:
    logger.error(f"SQL 文件不存在:{spark_path}")
    exit(1)

start_now = time.strftime("%Y-%m-%d %H:%M:%S", time.localtime())
logger.info(f"任务开始: {start_now}")

bucketed,bucket_keys=is_bucketed_table_and_get_bucket_keys(hive_db,table_name)
if bucketed:
    logger.error(f"禁止使用分桶表执行sparksql程序")
    exit(1)

if queue is None:
    queue = "auto"

if sql_type == "spark":
    logger.info(f"SPARK SQL 执行开始: {start_now}")

    if queue=="auto":
        queue=choose_yarn_queue()
    is_run_success=run_spark_cmd(date_var,time_s,spark_path,table_name,queue,executor_cores,driver_memory,executor_memory,num_executors,other_conf)
elif sql_type == "sparkcluster":
    logger.info(f"SPARK SQL cluster 执行开始: {start_now}")

    #获取分区字段值，只支持单分区或者非分区表
    hive_par = ""
    partitioned,partition_keys=is_partitioned_table_and_get_partition_keys(hive_db,table_name)
    if partitioned:
        hive_par = partition_keys[0][0]
    if len(partition_keys) != 1:
        logger.error(f"sparksqlcluster程序只支持单分区表")
        exit(1)
    logger.info(f"分区字段名：{hive_par}")
    if queue=="auto":
        queue=choose_yarn_queue()
    is_run_success=run_spark_cluster_cmd(date_var,time_s,spark_path,table_name,queue,executor_cores,driver_memory,executor_memory,num_executors,hive_db,hive_par,other_conf_hive,other_conf_spark)
else:
    logger.info(f"SQL_TYPE 配置错误，程序退出。")
    exit(1)

logger.info(f"spark任务执行结果：{is_run_success}")

if is_run_success == True:
    if starrocks_output !="0":
        starrocks_queue=choose_yarn_queue()
        is_run_success = run_hive_to_starrocks(starrocks_output_withpartition,time_s,hive_db,table_name,starrocks_queue,starrocks_executor_cores,starrocks_driver_memory,starrocks_executor_memory,starrocks_num_executors,starrocks_sink_columntypes,starrocks_sink_transform_map)
        if is_run_success == True:
            logger.info(f"导出到starrocks执行成功。")
        else:
            logger.error(f"导出到starrocks执行失败，程序退出。")
            exit(1)

    ## 任务完成反馈 starrocks插入数据
    now = time.strftime("%Y-%m-%d %H:%M:%S", time.localtime())
    insert_table_task(f"{hive_db}.{table_name}",time_s,start_now,now)
    now = time.strftime("%Y-%m-%d %H:%M:%S", time.localtime())
    logger.info(f"任务完成：{now}")
else:
    logger.error(f"sql文件：{spark_path}，sql命令执行失败，程序退出")
    exit(1)