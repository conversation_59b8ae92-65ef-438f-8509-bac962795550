# ads_bi_ad_short_cost_info_1d 脚本问题修复建议

## 问题分析

### 1. 主要错误
```
User class threw exception: java.lang.NoSuchMethodException: org.apache.hadoop.hive.ql.metadata.Hive.loadDynamicPartitions
```

这个错误通常由以下原因引起：
- Spark 与 Hive 版本不兼容
- 动态分区配置问题
- Metastore 版本配置错误

### 2. SQL 脚本问题
- 硬编码日期 `'2025-08-07'` 应该使用参数 `{date_var}`
- `insert overwrite` 语句缺少分区规范

## 已修复的问题

### 1. SQL 脚本修复
✅ 将所有硬编码日期替换为参数化日期 `{date_var}`
✅ 修改 `insert overwrite` 语句为 `insert overwrite table ads.ads_bi_ad_short_cost_info_1d partition(dt)`

### 2. 修复内容详情
- 修复了 `user_short_three_info_tmp` CTE 中的日期过滤条件
- 修复了 `short_flow_code_tmp` CTE 中的日期范围条件  
- 修复了 `ad_cost_amt_tmp` CTE 中的日期过滤条件
- 修复了主查询中的日期生成逻辑
- 添加了正确的分区插入语法

## 建议的额外修复

### 1. Spark 配置优化
在 `public_fun.py` 的 `run_spark_cluster_cmd` 函数中，建议添加以下配置：

```python
# 在 cmd 列表中添加以下配置
'--conf','spark.sql.adaptive.enabled=true',
'--conf','spark.sql.adaptive.coalescePartitions.enabled=true',
'--conf','spark.sql.hive.metastore.version=2.3.0',  # 根据实际 Hive 版本调整
'--conf','spark.sql.hive.metastore.jars=builtin',
'--conf','spark.serializer=org.apache.spark.serializer.KryoSerializer',
'--conf','spark.sql.sources.partitionOverwriteMode=dynamic'
```

### 2. 任务配置文件建议
在 `config/ads/ads_bi_ad_short_cost_info_1d.ini` 中建议添加：

```ini
[DEFAULT]
sql_type=sparkcluster
date_var=date_var
hive_db=ads
table_name=ads_bi_ad_short_cost_info_1d
queue=pro
executor_memory=10g
driver_memory=5g
num_executors=5
executor_cores=6
other_conf_hive=
other_conf_spark=--conf spark.sql.sources.partitionOverwriteMode=dynamic --conf spark.sql.hive.metastore.jars=builtin
```

### 3. 环境检查建议
1. 检查 Hive Metastore 版本与 Spark 配置的兼容性
2. 确认 HDFS 权限设置正确
3. 验证表结构与 SQL 脚本的一致性

## 测试建议

1. 先在测试环境执行修复后的脚本
2. 检查分区是否正确创建
3. 验证数据完整性
4. 确认性能表现

## 监控建议

1. 监控 Spark 应用程序日志
2. 检查 Yarn 队列资源使用情况
3. 关注数据倾斜问题
4. 监控执行时间和资源消耗