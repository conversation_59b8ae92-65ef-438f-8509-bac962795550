import sys
from datetime import datetime,timezone, timedelta, date 
from public_init import *
from public_fun import *
from public_logging import *
from public_task_table import *

# 设置参数 传参  参数1：库名 参数2：表名 参数3：执行日期yyyy-MM-dd
argv_list =  sys.argv
db_name = None
table_name = None
time_s = None

if len(argv_list) == 2:
    #db_name = argv_list[1]
    table_name = argv_list[1]
    time_s = (datetime.now(timezone.utc) - timedelta(hours=16) ).strftime('%Y-%m-%d')
elif len(argv_list) == 3:
    #db_name = argv_list[1]
    table_name = argv_list[1]
    time_s = argv_list[2]
else:
    print("参数个数错误，程序退出")
    exit()

#初始化log
initlog(table_name)

db_name = str(table_name).split('_')[0]

logger.info(f"入参：{db_name} {table_name} {time_s}")

#运行分区控制
time_now = (datetime.now(timezone.utc) + timedelta(hours=8) ).strftime('%Y%m%d')
logger.info(f"当前时间: {time_now}")

if int(time_now.replace('-','')) < int(time_s.replace('-','')):
    logger.info("禁止执行大于当天时间的分区")
    exit(1)
#运行分区控制

ini_filename = f"{CLUSTER_PATH}/config/{db_name}/{table_name}.ini"
if os.path.isfile(ini_filename):
    logger.info(f"任务配置文件存在:{ini_filename}")
else:
    logger.error(f"任务配置文件不存在:{ini_filename}")
    exit()

sql_type = read_config(ini_filename,"sql_type")
date_var = read_config(ini_filename,"date_var")
hive_db = read_config(ini_filename,"hive_db")
hive_db=hive_db.replace('\"', '')
table_name = read_config(ini_filename,"table_name")
table_name=table_name.replace('\"', '')
queue = read_config(ini_filename,"queue")
executor_memory = read_config(ini_filename,"executor_memory")
driver_memory = read_config(ini_filename,"driver_memory")
num_executors = read_config(ini_filename,"num_executors")
executor_cores = read_config(ini_filename,"executor_cores")
other_conf = read_config(ini_filename,"other_conf")
other_conf_hive = read_config(ini_filename,"other_conf_hive")
other_conf_spark = read_config(ini_filename,"other_conf_spark")

starrocks_output=read_config(ini_filename,"starrocks_output")
starrocks_output_withpartition=read_config(ini_filename,"starrocks_output_withpartition")
starrocks_queue=read_config(ini_filename,"starrocks_queue")
starrocks_executor_memory=read_config(ini_filename,"starrocks_executor_memory")
starrocks_driver_memory=read_config(ini_filename,"starrocks_driver_memory")
starrocks_num_executors=read_config(ini_filename,"starrocks_num_executors")
starrocks_executor_cores=read_config(ini_filename,"starrocks_executor_cores")
starrocks_sink_columntypes=read_config(ini_filename,"starrocks_sink_columntypes")
starrocks_sink_transform_map=read_config(ini_filename,"starrocks_sink_transform_map")

if executor_memory is None:
    executor_memory = "5g"
if driver_memory is None:
    driver_memory = "8g"
if num_executors is None:
    num_executors = "12"
if executor_cores is None:
    executor_cores = "1"
if other_conf is None:
    other_conf = ""
if other_conf_hive is None:
    other_conf_hive = ""
if other_conf_spark is None:
    other_conf_spark = ""

if starrocks_output is None:
    starrocks_output = "0"
if starrocks_output_withpartition is None:
    starrocks_output_withpartition="1"
if starrocks_queue is None:
    starrocks_queue="auto"
if starrocks_executor_memory is None:
    starrocks_executor_memory = "5g"
if starrocks_driver_memory is None:
    starrocks_driver_memory = "8g"
if starrocks_num_executors is None:
    starrocks_num_executors = "12"
if starrocks_executor_cores is None:
    starrocks_executor_cores = "1"
if starrocks_sink_columntypes is None:
    starrocks_sink_columntypes = ""
if starrocks_sink_transform_map is None:
    starrocks_sink_transform_map = ""

if sql_type is None:
    sql_type = "sparkcluster"

spark_path = f"{CLUSTER_PATH}/spark/{db_name}/{table_name}.sql"

if os.path.isfile(spark_path):
    logger.info(f"SQL 文件存在:{spark_path}")
else:
    logger.error(f"SQL 文件不存在:{spark_path}")
    exit(1)

start_now = time.strftime("%Y-%m-%d %H:%M:%S", time.localtime())
logger.info(f"任务开始: {start_now}")

bucketed,bucket_keys=is_bucketed_table_and_get_bucket_keys(hive_db,table_name)
if bucketed:
    logger.error(f"禁止使用分桶表执行sparksql程序")
    exit(1)

if queue is None:
    queue = "auto"

if sql_type == "spark":
    logger.info(f"SPARK SQL 执行开始: {start_now}")

    if queue=="auto":
        queue=choose_yarn_queue()
    is_run_success=run_spark_cmd(date_var,time_s,spark_path,table_name,queue,executor_cores,driver_memory,executor_memory,num_executors,other_conf)
elif sql_type == "sparkcluster":
    logger.info(f"SPARK SQL cluster 执行开始: {start_now}")

    #获取分区字段值，只支持单分区或者非分区表
    hive_par = ""
    partitioned,partition_keys=is_partitioned_table_and_get_partition_keys(hive_db,table_name)
    if partitioned:
        hive_par = partition_keys[0][0]
    if len(partition_keys) != 1:
        logger.error(f"sparksqlcluster程序只支持单分区表")
        exit(1)
    logger.info(f"分区字段名：{hive_par}")
    if queue=="auto":
        queue=choose_yarn_queue()
    is_run_success=run_spark_cluster_cmd(date_var,time_s,spark_path,table_name,queue,executor_cores,driver_memory,executor_memory,num_executors,hive_db,hive_par,other_conf_hive,other_conf_spark)
else:
    logger.info(f"SQL_TYPE 配置错误，程序退出。")
    exit(1)

logger.info(f"spark任务执行结果：{is_run_success}")

if is_run_success == True:
    if starrocks_output !="0":
        starrocks_queue=choose_yarn_queue()
        is_run_success = run_hive_to_starrocks(starrocks_output_withpartition,time_s,hive_db,table_name,starrocks_queue,starrocks_executor_cores,starrocks_driver_memory,starrocks_executor_memory,starrocks_num_executors,starrocks_sink_columntypes,starrocks_sink_transform_map)
        if is_run_success == True:
            logger.info(f"导出到starrocks执行成功。")
        else:
            logger.error(f"导出到starrocks执行失败，程序退出。")
            exit(1)

    ## 任务完成反馈 starrocks插入数据
    now = time.strftime("%Y-%m-%d %H:%M:%S", time.localtime())
    insert_table_task(f"{hive_db}.{table_name}",time_s,start_now,now)
    now = time.strftime("%Y-%m-%d %H:%M:%S", time.localtime())
    logger.info(f"任务完成：{now}")
else:
    logger.error(f"sql文件：{spark_path}，sql命令执行失败，程序退出")
    exit(1)



方法：

import re
import subprocess
import sys
from datetime import datetime,timezone, timedelta, date
import os
from pyhive import hive

from public_logging import *


#创建一个公共的python文件，用于存放公共的函数。

# 创建表名分割函数
def split_table_strings(original_string):
    # 去除字符串中多余的引号（如果它们是不需要的）
    cleaned_string = original_string.replace("'", "")

    # 使用split函数按照','拆分字符串
    split_tables = cleaned_string.split(',')

    # 使用列表推导式分别过滤出以'src'和'ods'开头的元素
    split_src_tables = [element.strip() for element in split_tables if element.strip().startswith(('src', 'ods'))]
    split_other_tables = [element.strip() for element in split_tables if not element.strip().startswith(('src', 'ods'))]

    # 使用join函数将过滤后的元素列表连接成字符串
    src_join_tables = "','".join(split_src_tables)
    other_join_tables = "','".join(split_other_tables)
    # 返回字符串
    return split_tables,src_join_tables, other_join_tables,split_src_tables,split_other_tables

def is_partitioned_table_and_get_partition_keys(db_name, table_name):
    # """
    # 使用Hive CLI查询Hive数据库，判断指定表是否为分区表，并获取分区字段。

    # :param hive_host: Hive服务的主机名
    # :param hive_port: Hive服务的端口（如果使用CLI，则可能不需要）
    # :param username: 连接Hive的用户名
    # :param db_name: Hive数据库名
    # :param table_name: Hive表名
    # :return: (bool, list) 元组，第一个元素为是否是分区表，第二个元素为分区字段列表
    # """
    # 注意：这里假设你已经配置了环境变量，使得hive命令可以直接在命令行中运行
    # 也可以修改为使用完整的Hive CLI或Beeline命令路径
    command = f"hive -e \"DESCRIBE {db_name}.{table_name};\""

    # 执行Hive命令
    try:
        result = subprocess.run(command, shell=True, stdout=subprocess.PIPE, stderr=subprocess.PIPE)


        if result.returncode != 0:
            raise Exception(f"Hive查询失败: {result.stderr}")

        # 解析输出结果
        lines = result.stdout.decode('utf8').split('\n')
        # print(f"lines:{lines}")
        partitioned = False
        partitioned_line = False
        partition_keys = []
        for line in lines:
            if line.strip().startswith('# Partition Information'):
                partitioned = True
            elif line.strip().startswith('# col_name') and partitioned:
                partitioned_line = True
            elif partitioned_line and partitioned:
                # 解析分区字段
                # partition_keys = [part.strip() for part in line.split('\t')[0]]
                # print(f"line:{line}")
                if line is None or line == "":
                    break
                partition_keys.append((line.split('\t')[0].strip(),line.split('\t')[1].strip()))

        return partitioned, partition_keys
    except Exception as e:
        print(f"获取分区信息错误，程序退出。Error: {e}")
        exit(1)

def is_bucketed_table_and_get_bucket_keys(db_name, table_name):
    # """
    # 使用Hive CLI查询Hive数据库，判断指定表是否为分区表，并获取分区字段。

    # :param hive_host: Hive服务的主机名
    # :param hive_port: Hive服务的端口（如果使用CLI，则可能不需要）
    # :param username: 连接Hive的用户名
    # :param db_name: Hive数据库名
    # :param table_name: Hive表名
    # :return: (bool, list) 元组，第一个元素为是否是分区表，第二个元素为分区字段列表
    # """
    # 注意：这里假设你已经配置了环境变量，使得hive命令可以直接在命令行中运行
    # 也可以修改为使用完整的Hive CLI或Beeline命令路径
    if table_name.endswith("_view"):
        return False, []
    if table_name.endswith("_merge"):
        table_name=table_name[:-6]
    if "dwd_app_event_all_server_time" in table_name:
        table_name="dwd_app_event_all_server_time"

    command = f"hive -e \"DESCRIBE FORMATTED {db_name}.{table_name};\""

    # 执行Hive命令
    try:
        result = subprocess.run(command, shell=True, stdout=subprocess.PIPE, stderr=subprocess.PIPE)


        if result.returncode != 0:
            raise Exception(f"Hive查询失败: {result.stderr}")

        # 解析输出结果
        lines = result.stdout.decode('utf8').split('\n')

        # print(f"lines:{lines}")
        bucketed = False
        bucket_keys = []
        for line in lines:
            if line.strip().startswith('Num Buckets:'):
                # print(line.split(':')[1].strip())
                if line.split(':')[1].strip()!='-1':
                    bucketed = True
            elif line.strip().startswith('Bucket Columns:') and bucketed:
                bucket_keys = line.split(':')[1].strip().strip('[]').split(', ')
                # print(f'bucket_columns:{bucket_keys}')
                break
        return bucketed, bucket_keys
    except Exception as e:
        print(f"获取分桶信息错误，程序退出。Error: {e}")
        exit(1)


# 执行 spark命令
def run_spark_cmd(date_var_name,date_var_value,sql_file_path,table_name,i_hive_queue_value,i_executor_cores_value,i_driver_memory_value,i_executor_memory_value,i_num_executors,i_other_conf):
    # 定义Hive命令和参数
    hive_command = 'spark-sql'
    hive_name = '--name'
    hive_master = '--master'
    hive_yarn = 'yarn'
    hive_queue = '--queue'
    hive_queue_value = i_hive_queue_value
    hive_executor_cores = '--executor-cores'
    executor_cores_value = i_executor_cores_value
    hive_driver = '--driver-memory'
    driver_memory_value = i_driver_memory_value
    hive_executor = '--executor-memory'
    executor_memory_value = i_executor_memory_value
    hive_num = '--num-executors'
    num_executors = i_num_executors
    hive_conf = '--hiveconf'
    # 关闭hive的文件权限继承 hive.warehouse.subdir.inherit.perms=false 来规避问题 setfacl: Permission denied
    hive_conf_value1 = 'mapreduce.fileoutputcommitter.algorithm.version=2'
    # spark读hive表无记录问题，子文件夹读取
    hive_conf_value2 = 'mapred.input.dir.recursive=true'
    hive_conf_value3 = 'hive.warehouse.subdir.inherit.perms=false'
    hive_conf_value4 = 'hive.exec.dynamic.partition.mode=nonstrict'
    hive_var = '--hivevar'
    spark_conf='--conf'
    # spark_con_value1='spark.sql.hive.metastore.jars=maven'
    spark_con_value1=f'spark.sql.hive.metastore.jars.path={CLUSTER_HIVE_METASTORE}/*'
    spark_con_value12='spark.sql.hive.metastore.jars=path'
    spark_con_value2='spark.sql.hive.metastore.version=1.2.1'
    spark_con_value3='spark.port.maxRetries=666'
    #spark_con_value4='spark.sql.session.timeZone=UTC'
    spark_con_value5='spark.sql.hive.convertMetastoreParquet=false'
    # 自适应部分
    spark_con_value6='spark.sql.adaptive.enabled=true'
    spark_con_value7='spark.sql.adaptive.coalescePartitions.enabled=true'
    spark_con_value8='spark.sql.hive.convertMetastoreOrc=false'
    spark_other_conf=i_other_conf

    # spark-sql --name  dwd_app_event_all_server_time_new --master yarn --queue pro --executor-cores 4 --driver-memory 8g --num-executors 100 --hiveconf spark.sql.session.timeZone=UTC --conf spark.sql.hive.metastore.jars=maven --conf spark.sql.hive.metastore.version=1.2.1 --conf spark.port.maxRetries=666 --hivevar date_var=20240807 -f /home/<USER>/jenkins/spark/dwd/dwd_app_event_all_server_time_new.sql
    # # 构建命令列表，注意列表中的每个元素都是命令或参数的一部分
    # 这里不需要对单个参数值（如日期变量值）进行引号处理，因为subprocess会正确处理它们
    cmd = [hive_command,
           hive_name,f'splocal_{table_name}',
           hive_master,
           hive_yarn,
           hive_queue,hive_queue_value,
           hive_executor_cores,executor_cores_value,
           hive_driver,
           driver_memory_value,
           hive_executor,
           executor_memory_value,
           hive_num,
           num_executors,
           hive_conf,hive_conf_value1,
           hive_conf,hive_conf_value2,
           hive_conf,hive_conf_value3,
           hive_conf,hive_conf_value4,
           spark_conf,spark_con_value1,
           spark_conf,spark_con_value12,
           spark_conf,spark_con_value2,
           spark_conf,spark_con_value3,
           #spark_conf,spark_con_value4,
           spark_conf,spark_con_value5,
           spark_conf,spark_con_value6,
           spark_conf,spark_con_value7,
           spark_conf,spark_con_value8]   + re.split(r'\s',spark_other_conf)  + [hive_var, f'{date_var_name}={date_var_value}', '-f', sql_file_path ]
    logger.info(f"执行spark命令：{cmd}")
    # 执行命令并捕获输出和错误（如果需要）
    try:
        # child = subprocess.Popen(cmd, stdout=subprocess.PIPE,stderr=subprocess.STDOUT,text=True,bufsize=-1)
        child = subprocess.Popen(cmd, stdout=subprocess.PIPE,stderr=subprocess.STDOUT,bufsize=-1)
    except Exception as e:
        logger.error(f"Error: {e}")
        exit()

    for x in child.stdout:
        logger.info('---> ' +  x.decode('utf8').rstrip('\n'))

    returncode = child.wait()

    logger.info(f"返回码：{returncode}")
    # 检查返回码
    if returncode == 0:
        logger.info("Hive命令执行成功")
        return True
    else:
        logger.error("Hive命令执行失败")
        return False

# 执行 spark-submit命令，运行sparksql脚本
def run_spark_cluster_cmd(date_var_name,date_var_value,sql_file_path,table_name,i_hive_queue_value,i_executor_cores_value,i_driver_memory_value,i_executor_memory_value,i_num_executors,hive_db,hive_par,other_conf_hive,other_conf_spark):
    # 定义Hive命令和参数
    spark_submit_queue_value = i_hive_queue_value
    spark_submit_executor_cores_value = i_executor_cores_value
    spark_submit_driver_memory_value = i_driver_memory_value
    spark_submit_executor_memory_value = i_executor_memory_value
    spark_submit_num_executors_value = i_num_executors
    sql = ""
    try:
        with open(sql_file_path, 'r') as file:
            # print("file:",file)
            for line in file:
                # 去除行首尾的空白字符（包括换行符）
                line = line.strip()
                # print("line",line)
                # 忽略空行和以#开头的注释行
                if not line or line.startswith('#') or line.startswith('--'):
                    continue
                sql += line + "\r\n"

    except FileNotFoundError:
        logger.error(f"文件未找到: {sql_file_path}")
        exit(1)

    sql = sql.replace("${", "{")
    sql = sql.replace("`", "")
    other_conf_hive = other_conf_hive.replace("--", "")

    if other_conf_spark == "":
        other_conf_spark = "--conf spark.port.maxRetries=666"

    cmd = ['spark-submit',
           '--name',f'spcluster_{table_name}',
           '--queue',spark_submit_queue_value,
           '--master','yarn',
           '--deploy-mode','cluster',
           '--driver-memory',spark_submit_driver_memory_value,
           '--executor-memory',spark_submit_executor_memory_value,
           '--executor-cores',spark_submit_executor_cores_value,
           '--num-executors',spark_submit_num_executors_value,
           '--conf','spark.port.maxRetries=666',
           '--conf','spark.sql.hive.convertMetastoreOrc=false',
           #'--conf','spark.sql.hive.metastore.jars=maven',
           #'--conf','spark.sql.hive.metastore.jars=path',
           #'--conf',f'spark.sql.hive.metastore.jars.path={CLUSTER_HIVE_METASTORE}/*',
           '--conf','spark.sql.hive.metastore.version=1.2.1'] + re.split(r'\s',other_conf_spark) + ['--class','com.spark.main.SparkSql',
           f'{CLUSTER_DEFAULT_FS}/dataware/jars/spark/SparkProject.jar',
           '--other_conf_hive',f'{other_conf_hive}',
           '--hivedb',f'{hive_db}',
           '--hivetable',f'{table_name}',
           '--hivepar',f'{hive_par}',
           '--hivevar',f'{date_var_name}={date_var_value}',
           '--defaultfs',f'{CLUSTER_DEFAULT_FS}',
           '--hivedir',f'{CLUSTER_HIVE_DIR}',
           '--sql', sql
           ]
    logger.info(f"执行spark-submit命令：{cmd}")
    # 执行命令并捕获输出和错误（如果需要）
    try:
        child = subprocess.Popen(cmd, stdout=subprocess.PIPE,stderr=subprocess.STDOUT,bufsize=-1)
    except Exception as e:
        logger.error(f"Error: {e}")
        exit(1)

    for x in child.stdout:
        logger.info('---> ' +  x.decode('utf8').rstrip('\n'))

    returncode = child.wait()

    logger.info(f"返回码：{returncode}")
    # 检查返回码
    if returncode == 0:
        logger.info("命令执行成功")
        return True
    else:
        logger.error("命令执行失败")
        return False

# 执行 spark-shell命令
def run_spark_shell_cmd(hive_db,hive_table,hive_partition):
    #os.environ['HADOOP_CLIENT_OPTS'] = '-Xmx4g'
    # 定义Hive命令和参数
    spark_shell = 'spark-shell'
    spark_shell_name = '--name'
    spark_shell_master = '--master'
    spark_shell_yarn = 'yarn'
    spark_shell_queue = '--queue'
    spark_shell_queue_pro = 'pro'
    spark_shell_driver_memory = '--driver-memory'
    spark_shell_driver_memory_8g = '8g'

    spark_shell_num_executors = '--num-executors'
    spark_shell_num_executors_100 = '100'
    spark_shell_conf='--conf'
    spark_shell_conf_value1='spark.port.maxRetries=666'
    spark_shell_echo = 'echo'
    # spark_shell_val = '\'val'
    spark_shell_mark = "'"

    spark_shell_cmd=f'val srp=spark.read.parquet("/warehouse/tablespace/managed/hive/{hive_db}.db/{hive_table}/ds={hive_partition}");srp.repartition(200).write.mode("overwrite").parquet("/warehouse/tablespace/managed/hive/{hive_db}.db/{hive_table}/ds={hive_partition}");'
    # spark-sql --name  dwd_app_event_all_server_time_new --master yarn --queue pro --executor-cores 4 --driver-memory 8g --num-executors 100 --hiveconf spark.sql.session.timeZone=UTC --conf spark.sql.hive.metastore.jars=maven --conf spark.sql.hive.metastore.version=1.2.1 --conf spark.port.maxRetries=666 --hivevar date_var=20240807 -f /home/<USER>/jenkins/spark/dwd/dwd_app_event_all_server_time_new.sql
    # # 构建命令列表，注意列表中的每个元素都是命令或参数的一部分
    # 这里不需要对单个参数值（如日期变量值）进行引号处理，因为subprocess会正确处理它们
    cmd_tmp = [spark_shell_echo,spark_shell_cmd]
    cmd = [spark_shell,
           spark_shell_name,f'merge_{hive_table}',
           spark_shell_master,spark_shell_yarn,
           spark_shell_queue,spark_shell_queue_pro,
           spark_shell_driver_memory,spark_shell_driver_memory_8g,
           spark_shell_num_executors,spark_shell_num_executors_100,
           spark_shell_conf,spark_shell_conf_value1
           ]
    logger.info(f"执行spark-shell命令：{cmd_tmp}")
    logger.info(f"执行spark-shell命令：{cmd}")
    # 执行命令并捕获输出和错误（如果需要）
    try:
        child_tmp = subprocess.Popen(cmd_tmp, stdout=subprocess.PIPE,stderr=subprocess.STDOUT,bufsize=-1)
        child = subprocess.Popen(cmd, stdin= child_tmp.stdout ,stdout=subprocess.PIPE,stderr=subprocess.STDOUT,bufsize=-1)
    except Exception as e:
        logger.error(f"Error: {e}")
        exit(1)

    for x in child.stdout:
        logger.info('---> ' +  x.decode('utf8').rstrip('\n'))

    returncode = child.wait()

    logger.info(f"返回码：{returncode}")
    # 检查返回码
    if returncode == 0:
        logger.info("命令执行成功")
        return True
    else:
        logger.error("命令执行失败")
        return False


# 执行 spark-submit命令,将数据导出到starrocks
def run_hive_to_starrocks(starrocks_output_withpartition,date_var_value,hive_db,table_name,i_queue_value,i_executor_cores_value,i_driver_memory_value,i_executor_memory_value,i_num_executors,starrocks_sink_columntypes,starrocks_sink_transform_map):
    # 定义Hive命令和参数
    spark_submit_queue_value = i_queue_value
    spark_submit_executor_cores_value = i_executor_cores_value
    spark_submit_driver_memory_value = i_driver_memory_value
    spark_submit_executor_memory_value = i_executor_memory_value
    spark_submit_num_executors_value = i_num_executors
    var_dt = f"dt='{date_var_value}'"
    date_var_value_short = date_var_value.replace('-','')
    var_p = f'p{date_var_value_short}'
    var_transform = "to_date(dt) dt"
    if starrocks_sink_transform_map != "":
        var_transform = var_transform + ',' + starrocks_sink_transform_map

    if starrocks_output_withpartition == "0":
        var_dt = f"dt='{date_var_value}'"
        var_p = f'p{date_var_value_short}'
    elif starrocks_output_withpartition == "1":
        var_dt = f"1=1"
        var_p = f'all'
        var_transform = starrocks_sink_transform_map
    else:
        logger.error(f"Error: {starrocks_output_withpartition}参数错误。")
        exit(1)
    cmd = ['spark-submit',
           '--queue',spark_submit_queue_value,
           '--master','yarn',
           '--deploy-mode','cluster',
           '--name',f'export_{table_name}',
           '--class','com.spark.main.Hive2StarRocks',
           '--driver-memory',spark_submit_driver_memory_value,
           '--executor-memory',spark_submit_executor_memory_value,
           '--executor-cores',spark_submit_executor_cores_value,
           '--num-executors',spark_submit_num_executors_value,
           '--conf','spark.port.maxRetries=666',
           '--jars','hdfs:///dataware/jars/spark/mysql-connector-java-8.0.20.jar,hdfs:///dataware/jars/spark/starrocks-spark-connector-3.3_2.12-1.1.2-1.1.2.jar',
           'hdfs:///dataware/jars/spark/SparkProject.jar',
           '--source.table',f'{hive_db}.{table_name}',
           '--source.condition',var_dt,
           '--sink.httpurl',f'{CLUSTER_STARROCKS_FE}',
           '--sink.jdbcurl',f'jdbc:mysql://{CLUSTER_STARROCKS_JDBC}',
           '--sink.username',f'{CLUSTER_STARROCKS_USER}',
           '--sink.password',f'{CLUSTER_STARROCKS_PASSWORD}',
           '--sink.table',f'ads.{table_name}',
           '--sink.deleteflag',var_p,
           '--sink.columntypes',f'{starrocks_sink_columntypes}',
           '--transform.map', var_transform
           ]
    cmdstr = f"执行spark-submit命令：{cmd}"
    logger.info(cmdstr.replace(CLUSTER_STARROCKS_PASSWORD, '********'))
    # 执行命令并捕获输出和错误（如果需要）
    try:
        child = subprocess.Popen(cmd, stdout=subprocess.PIPE,stderr=subprocess.STDOUT,bufsize=-1)
    except Exception as e:
        logger.error(f"Error: {e}")
        exit(1)

    for x in child.stdout:
        logger.info('---> ' +  x.decode('utf8').rstrip('\n'))

    returncode = child.wait()

    logger.info(f"返回码：{returncode}")
    # 检查返回码
    if returncode == 0:
        logger.info("命令执行成功")
        return True
    else:
        logger.error("命令执行失败")
        return False



# 执行 spark命令
def run_starrocks_cmd():
    logger.info("run_starrocks_cmd函数执行完成")
    return True


def split_and_categorize_strings(original_string):
    # 去除字符串中多余的引号（如果它们是不需要的）
    cleaned_string = original_string.replace("'", "")

    # 使用split函数按照','拆分字符串
    a = cleaned_string.split(',')

    # 使用列表推导式分别过滤出以'src'和'ods'开头的元素
    src_ods_elements = [element.strip() for element in a if element.strip().startswith(('src', 'ods'))]
    other_elements = [element.strip() for element in a if not element.strip().startswith(('src', 'ods'))]

    # 使用join函数将过滤后的元素列表连接成字符串
    s1 = "','".join(src_ods_elements)
    s2 = "','".join(other_elements)
    # 返回两个字符串
    return a,s1, s2

def subtract_one_day_from_yyyymmdd(date_str,interval_days):
    # 将字符串转换为datetime对象
    # 注意：因为datetime的strptime方法需要年-月-日的格式，我们需要先将字符串转换为这种格式
    # 这里我们使用字符串格式化来重新排列日期部分
    formatted_date_str = date_str[:4] + '-' + date_str[4:6] + '-' + date_str[6:]
    date = datetime.strptime(formatted_date_str, '%Y-%m-%d')

    # 减少一天
    new_date = date - timedelta(days=interval_days)

    # 将新的datetime对象转换回yyyymmdd格式的字符串
    new_date_str = new_date.strftime('%Y%m%d')

    return new_date_str


def get_yarn_queue_status(queue_name):
    result={}
    try:
        # 定义要执行的命令
        command = ["yarn", "queue", "-status", queue_name]

        # 使用Popen执行命令
        logger.info(f"执行yarn命令：{command}")
        process = subprocess.Popen(command, stdout=subprocess.PIPE, stderr=subprocess.PIPE)

    except Exception as e:
        # 捕获并输出任何异常
        print("An error occurred:", e)

    for x in process.stdout:
        line=x.decode('utf8')
        logger.info('---> ' +  line.rstrip('\n'))
        # 尝试匹配“键: 值”格式的行
        match = re.match(r"([^:]+):\s*(.+)", line)
        if match:
            key, value = match.groups()
            # 可能需要对值进行进一步的处理，比如去除引号、转换类型等
            # 这里简单地将值作为字符串存储
            key=key.strip(' \t')
            result[key] = value

        result[key]=value

    returncode = process.wait()
    logger.info(f"返回码：{returncode}")
    # 检查返回码
    if returncode == 0:
        logger.info("获取yarn队列状态命令执行成功")
    else:
        logger.error("获取yarn队列状态命令执行失败，程序退出")
        exit(1)
    # print(result)
    return result

def get_yarn_queue_accepted_num(queue_name):
    result=0
    try:
        # 定义要执行的命令
        command = f"yarn application -list |grep ACCEPTED | grep {queue_name} | wc -l"

        # 使用Popen执行命令
        logger.info(f"执行yarn命令：{command}")
        process = subprocess.Popen(command, stdout=subprocess.PIPE, stderr=subprocess.PIPE,shell=True)

    except Exception as e:
        # 捕获并输出任何异常
        print("An error occurred:", e)

    for x in process.stdout:
        line=x.decode('utf8')
        logger.info('---> ' +  line.rstrip('\n'))
        result=line

    returncode = process.wait()
    logger.info(f"返回码：{returncode}")
    # 检查返回码
    if returncode == 0:
        logger.info("获取yarn队列ACCEPTED状态任务数量，命令执行成功")
    else:
        logger.error("获取yarn队列ACCEPTED状态任务数量，命令执行失败，程序退出。")
        exit(1)
    return result

def choose_yarn_queue():
    queue_default="default"
    queue_pro="pro"
    pro_accepted = int(get_yarn_queue_accepted_num(queue_pro))
    default_accepted = int(get_yarn_queue_accepted_num(queue_default))
    logger.info(f"default队列ACCEPTED状态任务数量：{default_accepted}，pro队列ACCEPTED状态任务数量：{pro_accepted}")
    if pro_accepted != 0 and default_accepted != 0:
        logger.info("两个队列都繁忙，根据等待任务数量进行判断")
        if pro_accepted/7 > default_accepted/3:
            queue_name=queue_default
        else:
            queue_name=queue_pro
    else:
        logger.info("两个队列有空闲，根据使用百分比进行判断")
        default_info=get_yarn_queue_status(queue_default)
        pro_info=get_yarn_queue_status(queue_pro)
        default_current_capacity=float(default_info["Current Capacity"].strip('%'))
        pro_current_capacity=float(pro_info["Current Capacity"].strip('%'))
        default_free=(100 - default_current_capacity)*0.3
        pro_free=(100 - pro_current_capacity) * 0.7
        logger.info(f"default队列使用百分比:{30-default_free:.2f}，pro队列使用百分比:{70-pro_free:.2f}")
        logger.info(f"default队列剩余百分比:{default_free:.2f}，pro队列剩余百分比:{pro_free:.2f}")
        if default_free>pro_free :
            queue_name=queue_default
        else:
            queue_name=queue_pro
    logger.info(f"选择队列:{queue_name}")
    return queue_name

def read_config(filename, key_to_find):
    config = {}
    try:
        with open(filename, 'r') as file:
            # print("file:",file)
            for line in file:
                # 去除行首尾的空白字符（包括换行符）
                line = line.strip()
                # print("line",line)
                # 忽略空行和以#开头的注释行
                if not line or line.startswith('#'):
                    continue
                # 使用等号分割key和value
                key_value = line.split('=',maxsplit=1)
                # print("key_value",key_value)
                if len(key_value) == 2:
                    # 去除key和value两端的空白字符
                    key, value = map(str.strip, key_value)
                    #key, value = key_value[0],key_value[1]
                    config[key] = value
        # print("config:",config)
        # 检查传入的key是否在配置中
        if key_to_find in config:
            # print(f"key_to_find:{key_to_find},config[key]:{config[key_to_find]}")
            return config[key_to_find]
        else:
            return None  # 或者抛出异常，表示key不存在
    except FileNotFoundError:
        # print(f"配置文件 {filename} 未找到")
        return None

# main
if __name__ == "__main__":
    queue_name=choose_yarn_queue()
    print(queue_name)

