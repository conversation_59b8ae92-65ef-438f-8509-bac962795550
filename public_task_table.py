import pymysql
import sys
from public_init import *
from public_logging import *

sr_jdbc = CLUSTER_STARROCKS_JDBC.split(":")
sr_host = sr_jdbc[0]
sr_port = int(sr_jdbc[1])
sr_user = CLUSTER_STARROCKS_USER
sr_password = CLUSTER_STARROCKS_PASSWORD
sr_database = "ads"


#tablename ods.hi_short_play
#execution_date yyyy-MM-dd
#start_time yyyy-MM-dd HH:mm:ss
#end_time yyyy-MM-dd HH:mm:ss
def insert_table_task(tablename, execution_date, start_time, end_time):
    mysql_conn = pymysql.connect(host=sr_host,user=sr_user,password=sr_password,database=sr_database,port=sr_port)
    mysql_cursor = mysql_conn.cursor()
    insert_sql = f"insert into ads.ads_table_task(table_name,execution_date,start_time,end_time) values('{tablename}','{execution_date}','{start_time}','{end_time}');"
    logger.info(insert_sql)
    mysql_cursor.execute(insert_sql)
    mysql_conn.commit()
    mysql_cursor.close()
    mysql_conn.close()
