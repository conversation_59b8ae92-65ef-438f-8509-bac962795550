# ads_bi_ad_short_cost_info_1d 完整解决方案

## 问题根本原因

从最新的错误日志可以看出，问题的核心是：

```
java.lang.NoSuchMethodException: org.apache.hadoop.hive.ql.metadata.Hive.loadDynamicPartitions(org.apache.hadoop.fs.Path, java.lang.String, java.util.Map, boolean, int, boolean, boolean, boolean, long)
```

这是一个 **Spark 与 Hive 版本兼容性问题**，具体表现为：
- Spark 配置中使用了 `spark.sql.hive.metastore.version=1.2.1`
- 但实际的 Hive 版本可能是 2.x 或 3.x
- `loadDynamicPartitions` 方法的签名在不同 Hive 版本间发生了变化

## 立即解决方案

### 1. 修改 Spark 配置

在 `public_fun.py` 的 `run_spark_cluster_cmd` 函数中，需要修改 Hive 相关配置：

```python
# 将现有的配置
'--conf','spark.sql.hive.metastore.version=1.2.1'

# 替换为以下配置之一（根据实际 Hive 版本选择）：

# 选项1：使用内置 Hive 支持（推荐）
'--conf','spark.sql.hive.metastore.jars=builtin'

# 选项2：如果 Hive 版本是 2.3.x
'--conf','spark.sql.hive.metastore.version=2.3.0',
'--conf','spark.sql.hive.metastore.jars=builtin'

# 选项3：如果 Hive 版本是 3.x
'--conf','spark.sql.hive.metastore.version=3.1.0',
'--conf','spark.sql.hive.metastore.jars=builtin'
```

### 2. 添加动态分区配置

同时添加以下配置来正确处理动态分区：

```python
'--conf','spark.sql.sources.partitionOverwriteMode=dynamic',
'--conf','spark.sql.hive.manageFilesourcePartitions=true',
'--conf','spark.sql.hive.filesourcePartitionFileCacheSize=262144000'
```

### 3. 修改后的完整配置

```python
cmd = ['spark-submit',
       '--name',f'spcluster_{table_name}',
       '--queue',spark_submit_queue_value,
       '--master','yarn',
       '--deploy-mode','cluster',
       '--driver-memory',spark_submit_driver_memory_value,
       '--executor-memory',spark_submit_executor_memory_value,
       '--executor-cores',spark_submit_executor_cores_value,
       '--num-executors',spark_submit_num_executors_value,
       '--conf','spark.port.maxRetries=666',
       '--conf','spark.sql.hive.convertMetastoreOrc=false',
       '--conf','spark.sql.hive.metastore.jars=builtin',  # 关键修改
       '--conf','spark.sql.sources.partitionOverwriteMode=dynamic',  # 新增
       '--conf','spark.sql.hive.manageFilesourcePartitions=true',  # 新增
       '--conf','spark.serializer=org.apache.spark.serializer.KryoSerializer'] + re.split(r'\s',other_conf_spark) + [
       '--class','com.spark.main.SparkSql',
       f'{CLUSTER_DEFAULT_FS}/dataware/jars/spark/SparkProject.jar',
       '--other_conf_hive',f'{other_conf_hive}',
       '--hivedb',f'{hive_db}',
       '--hivetable',f'{table_name}',
       '--hivepar',f'{hive_par}',
       '--hivevar',f'{date_var_name}={date_var_value}',
       '--defaultfs',f'{CLUSTER_DEFAULT_FS}',
       '--hivedir',f'{CLUSTER_HIVE_DIR}',
       '--sql', sql
       ]
```

## 替代方案

如果上述配置修改仍然无法解决问题，可以考虑以下替代方案：

### 方案1：使用 INSERT INTO 替代 INSERT OVERWRITE

修改 SQL 脚本，先删除分区再插入：

```sql
-- 删除指定分区
set hive.exec.dynamic.partition=true;
set hive.exec.dynamic.partition.mode=nonstrict;

-- 先删除分区数据
ALTER TABLE ads.ads_bi_ad_short_cost_info_1d DROP IF EXISTS PARTITION (dt='{date_var}');
ALTER TABLE ads.ads_bi_ad_short_cost_info_1d DROP IF EXISTS PARTITION (dt=date_add('{date_var}', -1));
ALTER TABLE ads.ads_bi_ad_short_cost_info_1d DROP IF EXISTS PARTITION (dt=date_add('{date_var}', -2));

-- 然后插入数据
INSERT INTO ads.ads_bi_ad_short_cost_info_1d PARTITION (dt)
SELECT ... -- 原有的查询逻辑
```

### 方案2：使用 Spark DataFrame API

如果可能，考虑将 SQL 逻辑改写为 Spark DataFrame API，避免使用 Hive 的动态分区功能。

## 验证步骤

1. **检查 Hive 版本**：
   ```bash
   hive --version
   ```

2. **检查 Spark 版本**：
   ```bash
   spark-submit --version
   ```

3. **测试配置**：
   先在测试环境使用简单的 INSERT OVERWRITE 语句测试新配置是否有效。

## 监控建议

1. 关注 Spark 应用程序的详细日志
2. 检查 Hive Metastore 的连接状态
3. 监控 YARN 资源使用情况
4. 验证分区数据的完整性

## 长期解决方案

1. **统一版本管理**：确保 Spark 和 Hive 版本兼容
2. **配置标准化**：建立统一的 Spark 配置模板
3. **测试自动化**：建立自动化测试流程验证版本兼容性