/**
CREATE TABLE ads.ads_bi_ad_short_cost_info_1d(
   `short_play_code` string COMMENT '剧id'
  ,`app_language` varchar(65533) COMMENT '界面语言'
  ,`short_play_name` varchar(65533) COMMENT '剧名'
  ,`short_play_name_alias` varchar(65533) COMMENT '别名'
  ,`short_play_type` string COMMENT '短剧类型 1-自制剧 2-解说剧 3-配音翻译剧 ,4-字幕翻译剧，5-版权剧,6-测试剧，7-AI翻译剧，8-自制翻译剧 '
  ,`language_name` string COMMENT '语言'
  ,`pub_time` string COMMENT '上线时间'
  ,`cost_amt` decimal(20, 6) COMMENT '投放消耗($)'
  ,`viewers_unt` bigint COMMENT '播放人数'
  ,`short_play_one_code` string COMMENT '最多流向剧code'
  ,`viewers_unt_one` bigint COMMENT '最多流向用户数'
  ,`short_play_two_code` string COMMENT '第二流向剧code'
  ,`viewers_unt_two` bigint COMMENT '第二流向用户数'
  ,`short_play_three_code` string COMMENT '第三流向剧code'
  ,`viewers_unt_three` bigint COMMENT '第三流向用户数'
  ,`etl_tm` string  COMMENT "清洗时间"
 )
COMMENT '短剧流转情况分析'
PARTITIONED BY (
  `dt` string COMMENT '事件事件' );
*/

-- 设置Hive兼容性参数，避免动态分区问题
set hive.exec.dynamic.partition=false;
set hive.exec.dynamic.partition.mode=strict;
set mapred.job.queue.name=pro;

-- 删除目标分区（如果存在）
alter table ads.ads_bi_ad_short_cost_info_1d drop if exists partition(dt='${date_var}');

-- 创建临时表存储最终结果，避免复杂的CTE嵌套
drop table if exists temp_ads_bi_ad_short_cost_info_result;
create table temp_ads_bi_ad_short_cost_info_result as
with user_short_three_info_tmp as (
    -- 获取近3天观剧详情，且播放时长>=5秒
    select
      dt,
      user_id,
      app_language,
      short_play_code
    from dwm.dwm_watch_user_play_time_real_1d
    where dt >= date_add('${date_var}', -2) and dt <= '${date_var}'
      and eff_watch_cnt > 0
      and scene = 'immersion'
    group by dt, user_id, app_language, short_play_code
  ),
  total_short_viewers_tmp as (
    -- 当天播放每个剧的有效观看人数
    select
      dt,
      short_play_code,
      app_language,
      count(distinct user_id) as total_viewers_unt
    from user_short_three_info_tmp
    group by dt, short_play_code, app_language
  ),
  short_flow_code_tmp as (
    -- 构建流向关系
    select
      t.dt,
      t.short_play_code,
      t.app_language,
      p.user_id,
      p.short_play_code as flow_code
    from user_short_three_info_tmp t
    inner join dwm.dwm_watch_user_play_time_real_1d p
      on p.user_id = t.user_id
    where p.dt between date_add('${date_var}', -2) and date_add('${date_var}', 2)
      and p.dt between t.dt and date_add(t.dt, 2)
      and p.eff_watch_cnt > 0
      and p.scene = 'immersion'
      and p.short_play_code <> t.short_play_code
  ),
  flow_code_user_unt as (
    -- 统计流向Top3
    select
      dt,
      short_play_code,
      app_language,
      max(case when rn = 1 then flow_code end) as short_play_one_code,
      max(case when rn = 2 then flow_code end) as short_play_two_code,
      max(case when rn = 3 then flow_code end) as short_play_three_code,
      sum(case when rn = 1 then viewers_unt end) as viewers_unt_one,
      sum(case when rn = 2 then viewers_unt end) as viewers_unt_two,
      sum(case when rn = 3 then viewers_unt end) as viewers_unt_three
    from (
      select
        dt,
        short_play_code,
        app_language,
        flow_code,
        count(distinct user_id) as viewers_unt,
        row_number() over (partition by dt, short_play_code, app_language order by count(distinct user_id) desc) as rn
      from short_flow_code_tmp
      group by dt, short_play_code, app_language, flow_code
    ) tt
    where rn <= 3
    group by dt, short_play_code, app_language
  ),
  ad_cost_amt_tmp as (
    -- 广告投放消耗
    select
      dt,
      short_play_code,
      app_language,
      sum(cost_amt) as cost_amt
    from dwm.dwm_ad_advert_place_cost_1d
    where dt >= date_add('${date_var}', -2) and dt <= '${date_var}'
      and short_play_id is not null
    group by dt, short_play_code, app_language
  ),
  date_play_combinations as (
    -- 生成所有需要的日期和剧集组合
    select '${date_var}' as dt, short_play_code from dim.dim_short_play_id_info
    union all
    select date_add('${date_var}', -1) as dt, short_play_code from dim.dim_short_play_id_info
    union all
    select date_add('${date_var}', -2) as dt, short_play_code from dim.dim_short_play_id_info
  ),
  aggregated_data as (
    select
      coalesce(t.short_play_code, t1.short_play_code, t2.short_play_code, tt.short_play_code, '') as short_play_code,
      coalesce(t.app_language, t1.app_language, t2.app_language, '') as app_language,
      tt.dt,
      nvl(t2.cost_amt, 0) as cost_amt,
      nvl(t.total_viewers_unt, 0) as viewers_unt,
      t1.short_play_one_code,
      nvl(t1.viewers_unt_one, 0) as viewers_unt_one,
      t1.short_play_two_code,
      nvl(t1.viewers_unt_two, 0) as viewers_unt_two,
      t1.short_play_three_code,
      nvl(t1.viewers_unt_three, 0) as viewers_unt_three,
      from_unixtime(unix_timestamp()) as etl_tm
    from date_play_combinations tt
    left join total_short_viewers_tmp t 
      on tt.short_play_code = t.short_play_code and tt.dt = t.dt
    left join flow_code_user_unt t1 
      on tt.short_play_code = t1.short_play_code 
      and nvl(t.app_language, '') = nvl(t1.app_language, '') 
      and tt.dt = t1.dt
    left join ad_cost_amt_tmp t2 
      on tt.short_play_code = t2.short_play_code 
      and nvl(t.app_language, '') = nvl(t2.app_language, '') 
      and tt.dt = t2.dt
  )
select
  a.short_play_code,
  a.app_language,
  b.short_play_name,
  b.short_play_name_alias,
  b.short_play_type,
  cast(b.language_name as string) as language_name,
  b.pub_time,
  a.cost_amt,
  a.viewers_unt,
  a.short_play_one_code,
  a.viewers_unt_one,
  a.short_play_two_code,
  a.viewers_unt_two,
  a.short_play_three_code,
  a.viewers_unt_three,
  a.etl_tm,
  a.dt
from aggregated_data a
left join dim.dim_short_play_id_info b on a.short_play_code = b.short_play_code
where trim(a.short_play_code) <> '';

-- 使用简单的INSERT INTO避免动态分区问题
insert into table ads.ads_bi_ad_short_cost_info_1d partition(dt='${date_var}')
select 
  short_play_code,
  app_language,
  short_play_name,
  short_play_name_alias,
  short_play_type,
  language_name,
  pub_time,
  cost_amt,
  viewers_unt,
  short_play_one_code,
  viewers_unt_one,
  short_play_two_code,
  viewers_unt_two,
  short_play_three_code,
  viewers_unt_three,
  etl_tm
from temp_ads_bi_ad_short_cost_info_result
where dt = '${date_var}';

-- 清理临时表
drop table if exists temp_ads_bi_ad_short_cost_info_result;
