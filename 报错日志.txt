[LOG-PATH]: /usr/hdp/*******-007/dolphinscheduler/worker-server/logs/20250809/18554149119168/7/26526/74520.log, [HOST]:  *********:1234

[INFO] 2025-08-09 16:31:22.843 +0800 - ***********************************************************************************************

[INFO] 2025-08-09 16:31:22.845 +0800 - *********************************  Initialize task context  ***********************************

[INFO] 2025-08-09 16:31:22.845 +0800 - ***********************************************************************************************

[INFO] 2025-08-09 16:31:22.845 +0800 - Begin to initialize task

[INFO] 2025-08-09 16:31:22.845 +0800 - Set task startTime: 1754728282845

[INFO] 2025-08-09 16:31:22.845 +0800 - Set task appId: 26526_74520

[INFO] 2025-08-09 16:31:22.845 +0800 - End initialize task {

  "taskInstanceId" : 74520,

  "taskName" : "ads_bi_ad_short_cost_info_1d",

  "firstSubmitTime" : 1754728282811,

  "startTime" : 1754728282845,

  "taskType" : "SHELL",

  "workflowInstanceHost" : "*********:5678",

  "host" : "*********:1234",

  "logPath" : "/usr/hdp/*******-007/dolphinscheduler/worker-server/logs/20250809/18554149119168/7/26526/74520.log",

  "processId" : 0,

  "processDefineCode" : 18554149119168,

  "processDefineVersion" : 7,

  "processInstanceId" : 26526,

  "scheduleTime" : 0,

  "globalParams" : "[{\"prop\":\"date_var\",\"direct\":\"IN\",\"type\":\"VARCHAR\",\"value\":\"$[yyyy-MM-dd-1]\"}]",

  "executorId" : 6,

  "cmdTypeIfComplement" : 0,

  "tenantCode" : "default",

  "processDefineId" : 0,

  "projectId" : 0,

  "projectCode" : 18495586806848,

  "taskParams" : "{\"localParams\":[],\"rawScript\":\"echo \\\"===============start node  ${system.task.definition.name}=========================\\\"\\r\\n\\r\\nbiz_date=${date_var}\\r\\ntask_name=${system.task.definition.name}\\r\\ndatabase=${task_name%.*}\\r\\ntable=${task_name#*.}\\r\\n\\r\\necho $database\\r\\necho $table\\r\\n\\r\\nset -e\\r\\npython3 /data/dataware/batchdatawarehouse/python/public_run_sql.py $table ${date_var}\\r\\n\\r\\necho \\\"===============end node  ${system.task.definition.name}=========================\\\"\",\"resourceList\":[]}",

  "prepareParamsMap" : {

    "system.task.definition.name" : {

      "prop" : "system.task.definition.name",

      "direct" : "IN",

      "type" : "VARCHAR",

      "value" : "ads_bi_ad_short_cost_info_1d"

    },

    "system.project.name" : {

      "prop" : "system.project.name",

      "direct" : "IN",

      "type" : "VARCHAR",

      "value" : null

    },

    "date_var" : {

      "prop" : "date_var",

      "direct" : "IN",

      "type" : "VARCHAR",

      "value" : "2025-08-08"

    },

    "system.project.code" : {

      "prop" : "system.project.code",

      "direct" : "IN",

      "type" : "VARCHAR",

      "value" : "18495586806848"

    },

    "system.workflow.instance.id" : {

      "prop" : "system.workflow.instance.id",

      "direct" : "IN",

      "type" : "VARCHAR",

      "value" : "26526"

    },

    "system.biz.curdate" : {

      "prop" : "system.biz.curdate",

      "direct" : "IN",

      "type" : "VARCHAR",

      "value" : "20250809"

    },

    "system.biz.date" : {

      "prop" : "system.biz.date",

      "direct" : "IN",

      "type" : "VARCHAR",

      "value" : "20250808"

    },

    "system.task.instance.id" : {

      "prop" : "system.task.instance.id",

      "direct" : "IN",

      "type" : "VARCHAR",

      "value" : "74520"

    },

    "system.workflow.definition.name" : {

      "prop" : "system.workflow.definition.name",

      "direct" : "IN",

      "type" : "VARCHAR",

      "value" : "tbl_ads_bi_ad_short_cost_info_1d_import"

    },

    "system.task.definition.code" : {

      "prop" : "system.task.definition.code",

      "direct" : "IN",

      "type" : "VARCHAR",

      "value" : "18554126580672"

    },

    "system.workflow.definition.code" : {

      "prop" : "system.workflow.definition.code",

      "direct" : "IN",

      "type" : "VARCHAR",

      "value" : "18554149119168"

    },

    "system.datetime" : {

      "prop" : "system.datetime",

      "direct" : "IN",

      "type" : "VARCHAR",

      "value" : "20250809163122"

    }

  },

  "taskAppId" : "26526_74520",

  "taskTimeout" : 2147483647,

  "workerGroup" : "default",

  "delayTime" : 0,

  "currentExecutionStatus" : "SUBMITTED_SUCCESS",

  "endTime" : 0,

  "varPool" : "[]",

  "dryRun" : 0,

  "paramsMap" : { },

  "cpuQuota" : -1,

  "memoryMax" : -1,

  "testFlag" : 0,

  "logBufferEnable" : false,

  "dispatchFailTimes" : 0

}

[INFO] 2025-08-09 16:31:22.846 +0800 - ***********************************************************************************************

[INFO] 2025-08-09 16:31:22.846 +0800 - *********************************  Load task instance plugin  *********************************

[INFO] 2025-08-09 16:31:22.846 +0800 - ***********************************************************************************************

[INFO] 2025-08-09 16:31:22.848 +0800 - Send task status RUNNING_EXECUTION master: *********:1234

[INFO] 2025-08-09 16:31:22.848 +0800 - TenantCode: default check successfully

[INFO] 2025-08-09 16:31:22.848 +0800 - WorkflowInstanceExecDir: /tmp/dolphinscheduler/exec/process/default/18495586806848/18554149119168_7/26526/74520 check successfully

[INFO] 2025-08-09 16:31:22.848 +0800 - Create TaskChannel: org.apache.dolphinscheduler.plugin.task.shell.ShellTaskChannel successfully

[INFO] 2025-08-09 16:31:22.848 +0800 - Download resources successfully: 

ResourceContext(resourceItemMap={})

[INFO] 2025-08-09 16:31:22.848 +0800 - Download upstream files: [] successfully

[INFO] 2025-08-09 16:31:22.849 +0800 - Task plugin instance: SHELL create successfully

[INFO] 2025-08-09 16:31:22.849 +0800 - Initialize shell task params {

  "localParams" : [ ],

  "varPool" : null,

  "rawScript" : "echo \"===============start node  ${system.task.definition.name}=========================\"\r\n\r\nbiz_date=${date_var}\r\ntask_name=${system.task.definition.name}\r\ndatabase=${task_name%.*}\r\ntable=${task_name#*.}\r\n\r\necho $database\r\necho $table\r\n\r\nset -e\r\npython3 /data/dataware/batchdatawarehouse/python/public_run_sql.py $table ${date_var}\r\n\r\necho \"===============end node  ${system.task.definition.name}=========================\"",

  "resourceList" : [ ]

}

[INFO] 2025-08-09 16:31:22.849 +0800 - Success initialized task plugin instance successfully

[INFO] 2025-08-09 16:31:22.849 +0800 - Set taskVarPool: [] successfully

[INFO] 2025-08-09 16:31:22.849 +0800 - ***********************************************************************************************

[INFO] 2025-08-09 16:31:22.849 +0800 - *********************************  Execute task instance  *************************************

[INFO] 2025-08-09 16:31:22.849 +0800 - ***********************************************************************************************

[INFO] 2025-08-09 16:31:22.850 +0800 - Final Shell file is: 

[INFO] 2025-08-09 16:31:22.850 +0800 - ****************************** Script Content *****************************************************************

[INFO] 2025-08-09 16:31:22.850 +0800 - #!/bin/bash

BASEDIR=$(cd `dirname $0`; pwd)

cd $BASEDIR

echo "===============start node  ads_bi_ad_short_cost_info_1d========================="


biz_date=2025-08-08

task_name=ads_bi_ad_short_cost_info_1d

database=${task_name%.*}

table=${task_name#*.}


echo $database

echo $table


set -e

python3 /data/dataware/batchdatawarehouse/python/public_run_sql.py $table 2025-08-08


echo "===============end node  ads_bi_ad_short_cost_info_1d========================="

[INFO] 2025-08-09 16:31:22.850 +0800 - ****************************** Script Content *****************************************************************

[INFO] 2025-08-09 16:31:22.850 +0800 - Executing shell command : bash /tmp/dolphinscheduler/exec/process/default/18495586806848/18554149119168_7/26526/74520/26526_74520.sh

[INFO] 2025-08-09 16:31:22.857 +0800 - process start, process id is: 195467

[INFO] 2025-08-09 16:31:23.857 +0800 -  -> 

	===============start node  ads_bi_ad_short_cost_info_1d=========================

	ads_bi_ad_short_cost_info_1d

	ads_bi_ad_short_cost_info_1d

	************LOG*************

	

	/data/dataware/batchdatawarehouse/logs/20250809/ads_bi_ad_short_cost_info_1d_1754728283.0144854.log

	

	**********LOG**************

	2025-08-09 16:31:23,014 - INFO - 入参：ads ads_bi_ad_short_cost_info_1d 2025-08-08

	2025-08-09 16:31:23,014 - INFO - 当前时间: 20250809

	2025-08-09 16:31:23,014 - INFO - 任务配置文件存在:/data/dataware/batchdatawarehouse/config/ads/ads_bi_ad_short_cost_info_1d.ini

	2025-08-09 16:31:23,016 - INFO - SQL 文件存在:/data/dataware/batchdatawarehouse/spark/ads/ads_bi_ad_short_cost_info_1d.sql

	2025-08-09 16:31:23,016 - INFO - 任务开始: 2025-08-09 16:31:23

[INFO] 2025-08-09 16:31:36.859 +0800 -  -> 

	2025-08-09 16:31:36,336 - INFO - SPARK SQL cluster 执行开始: 2025-08-09 16:31:23

[INFO] 2025-08-09 16:31:49.860 +0800 -  -> 

	2025-08-09 16:31:49,447 - INFO - 分区字段名：dt

	2025-08-09 16:31:49,448 - INFO - 执行yarn命令：yarn application -list |grep ACCEPTED | grep pro | wc -l

[INFO] 2025-08-09 16:31:51.861 +0800 -  -> 

	2025-08-09 16:31:51,661 - INFO - ---> 0

	2025-08-09 16:31:51,662 - INFO - 返回码：0

	2025-08-09 16:31:51,662 - INFO - 获取yarn队列ACCEPTED状态任务数量，命令执行成功

	2025-08-09 16:31:51,662 - INFO - 执行yarn命令：yarn application -list |grep ACCEPTED | grep default | wc -l

[INFO] 2025-08-09 16:31:53.861 +0800 -  -> 

	2025-08-09 16:31:53,766 - INFO - ---> 0

	2025-08-09 16:31:53,766 - INFO - 返回码：0

	2025-08-09 16:31:53,766 - INFO - 获取yarn队列ACCEPTED状态任务数量，命令执行成功

	2025-08-09 16:31:53,766 - INFO - default队列ACCEPTED状态任务数量：0，pro队列ACCEPTED状态任务数量：0

	2025-08-09 16:31:53,766 - INFO - 两个队列有空闲，根据使用百分比进行判断

	2025-08-09 16:31:53,766 - INFO - 执行yarn命令：['yarn', 'queue', '-status', 'default']

[INFO] 2025-08-09 16:31:55.862 +0800 -  -> 

	2025-08-09 16:31:55,840 - INFO - ---> Queue Information : 

	2025-08-09 16:31:55,841 - INFO - ---> Queue Name : default

	2025-08-09 16:31:55,841 - INFO - ---> 	State : RUNNING

	2025-08-09 16:31:55,841 - INFO - ---> 	Capacity : 30.00%

	2025-08-09 16:31:55,842 - INFO - ---> 	Current Capacity : 2.04%

	2025-08-09 16:31:55,842 - INFO - ---> 	Maximum Capacity : 30.00%

	2025-08-09 16:31:55,842 - INFO - ---> 	Default Node Label expression : <DEFAULT_PARTITION>

	2025-08-09 16:31:55,842 - INFO - ---> 	Accessible Node Labels : *

	2025-08-09 16:31:55,842 - INFO - ---> 	Preemption : enabled

	2025-08-09 16:31:55,842 - INFO - ---> 	Intra-queue Preemption : enabled

[INFO] 2025-08-09 16:31:56.862 +0800 -  -> 

	2025-08-09 16:31:55,919 - INFO - 返回码：0

	2025-08-09 16:31:55,920 - INFO - 获取yarn队列状态命令执行成功

	2025-08-09 16:31:55,920 - INFO - 执行yarn命令：['yarn', 'queue', '-status', 'pro']

[INFO] 2025-08-09 16:31:58.863 +0800 -  -> 

	2025-08-09 16:31:58,045 - INFO - ---> Queue Information : 

	2025-08-09 16:31:58,045 - INFO - ---> Queue Name : pro

	2025-08-09 16:31:58,045 - INFO - ---> 	State : RUNNING

	2025-08-09 16:31:58,045 - INFO - ---> 	Capacity : 70.00%

	2025-08-09 16:31:58,046 - INFO - ---> 	Current Capacity : .87%

	2025-08-09 16:31:58,046 - INFO - ---> 	Maximum Capacity : 70.00%

	2025-08-09 16:31:58,046 - INFO - ---> 	Default Node Label expression : <DEFAULT_PARTITION>

	2025-08-09 16:31:58,046 - INFO - ---> 	Accessible Node Labels : *

	2025-08-09 16:31:58,046 - INFO - ---> 	Preemption : enabled

	2025-08-09 16:31:58,046 - INFO - ---> 	Intra-queue Preemption : enabled

	2025-08-09 16:31:58,128 - INFO - 返回码：0

	2025-08-09 16:31:58,128 - INFO - 获取yarn队列状态命令执行成功

	2025-08-09 16:31:58,129 - INFO - default队列使用百分比:0.61，pro队列使用百分比:0.61

	2025-08-09 16:31:58,129 - INFO - default队列剩余百分比:29.39，pro队列剩余百分比:69.39

	2025-08-09 16:31:58,129 - INFO - 选择队列:pro

	2025-08-09 16:31:58,130 - INFO - 执行spark-submit命令：['spark-submit', '--name', 'spcluster_ads_bi_ad_short_cost_info_1d', '--queue', 'pro', '--master', 'yarn', '--deploy-mode', 'cluster', '--driver-memory', '5g', '--executor-memory', '10g', '--executor-cores', '6', '--num-executors', '5', '--conf', 'spark.port.maxRetries=666', '--conf', 'spark.sql.hive.convertMetastoreOrc=false', '--conf', 'spark.sql.hive.metastore.version=1.2.1', '--conf', 'spark.port.maxRetries=666', '--class', 'com.spark.main.SparkSql', 'hdfs://shffff/dataware/jars/spark/SparkProject.jar', '--other_conf_hive', '', '--hivedb', 'ads', '--hivetable', 'ads_bi_ad_short_cost_info_1d', '--hivepar', 'dt', '--hivevar', 'date_var=2025-08-08', '--defaultfs', 'hdfs://shffff', '--hivedir', '/warehouse/tablespace/managed/hive', '--sql', '/**\r\nCREATE TABLE ads.ads_bi_ad_short_cost_info_1d(\r\nshort_play_code string COMMENT \'剧id\'\r\n,app_language varchar(65533) COMMENT \'界面语言\'\r\n,short_play_name varchar(65533) COMMENT \'剧名\'\r\n,short_play_name_alias varchar(65533) COMMENT \'别名\'\r\n,short_play_type string COMMENT \'短剧类型 1-自制剧 2-解说剧 3-配音翻译剧 ,4-字幕翻译剧，5-版权剧,6-测试剧，7-AI翻译剧，8-自制翻译剧 \'\r\n,language_name string COMMENT \'语言\'\r\n,pub_time string COMMENT \'上线时间\'\r\n,cost_amt decimal(20, 6) COMMENT \'投放消耗($)\'\r\n,viewers_unt bigint COMMENT \'播放人数\'\r\n,short_play_one_code string COMMENT \'最多流向剧code\'\r\n,viewers_unt_one bigint COMMENT \'最多流向用户数\'\r\n,short_play_two_code string COMMENT \'第二流向剧code\'\r\n,viewers_unt_two bigint COMMENT \'第二流向用户数\'\r\n,short_play_three_code string COMMENT \'第三流向剧code\'\r\n,viewers_unt_three bigint COMMENT \'第三流向用户数\'\r\n,etl_tm string  COMMENT "清洗时间"\r\n)\r\nCOMMENT \'短剧流转情况分析\'\r\nPARTITIONED BY (\r\ndt string COMMENT \'事件事件\' );\r\n*/\r\nwith user_short_three_info_tmp as (   -- 需要当天播放数据\r\nselect\r\ndt,\r\nuser_id,\r\napp_language,\r\nshort_play_code  as short_play_code\r\nfrom dwm.dwm_watch_user_play_time_real_1d -- 观看主表\r\nwhere dt >= date_add(\'2025-08-07\', -2) and dt <= \'2025-08-07\'\r\nand eff_watch_cnt > 0 -- 有效观看\r\nand scene = \'immersion\' -- 沉浸页\r\ngroup by dt, user_id, app_language, short_play_code\r\n),\r\ntotal_short_viewers_tmp as ( -- 当天播放每个剧的有效观看人数\r\nselect\r\ndt,\r\nshort_play_code,-- 剧code\r\napp_language,-- app语言\r\ncount(distinct user_id) as total_viewers_unt -- 观看用户数\r\nfrom user_short_three_info_tmp\r\ngroup by dt, short_play_code, app_language\r\n),\r\nshort_flow_code_tmp as (\r\nselect\r\nt.dt,\r\nt.short_play_code,\r\nt.app_language,\r\np.user_id,\r\np.short_play_code as flow_code\r\nfrom user_short_three_info_tmp t\r\ninner join dwm.dwm_watch_user_play_time_real_1d p\r\non p.user_id = t.user_id -- 同一用户\r\nwhere p.dt between date_add(\'2025-08-07\', -2) and date_add(\'2025-08-07\', 2) -- 前两天到后两天之间\r\nand p.dt between t.dt and date_add(t.dt, 2)\r\nand p.eff_watch_cnt > 0 -- 有效观看\r\nand scene = \'immersion\' -- 沉浸页\r\nand p.short_play_code <> t.short_play_code\r\n),\r\nflow_code_user_unt as (  -- 统计流向Top3\r\nselect\r\ndt,\r\nshort_play_code as short_play_code,\r\napp_language, -- app语言\r\nmax(case when rn = 1 then flow_code end) as short_play_one_code, -- 最多流向剧code\r\nmax(case when rn = 2 then flow_code end) as short_play_two_code, -- 第二流向剧code\r\nmax(case when rn = 3 then flow_code end) as short_play_three_code, -- 第三流向剧code\r\nsum(case when rn = 1 then viewers_unt end) as viewers_unt_one, -- 最多流向用户数\r\nsum(case when rn = 2 then viewers_unt end) as viewers_unt_two, -- 第二流向用户数\r\nsum(case when rn = 3 then viewers_unt end) as viewers_unt_three -- 第三流向用户数\r\nfrom (\r\nselect\r\ndt,\r\nshort_play_code,\r\napp_language,\r\nflow_code, -- 流向code\r\ncount(distinct user_id) as viewers_unt,\r\nrow_number() over (partition by dt, short_play_code, app_language order by count(distinct user_id) desc) as rn\r\nfrom short_flow_code_tmp -- 流向关系表\r\ngroup by dt, short_play_code, app_language, flow_code\r\n) tt\r\nwhere rn <= 3\r\ngroup by dt, short_play_code, app_language\r\n),\r\nad_cost_amt_tmp as (  -- 需要严格限制当天数据\r\nselect\r\ndt,\r\nshort_play_code,\r\napp_language,\r\nsum(cost_amt) as cost_amt\r\nfrom dwm.dwm_ad_advert_place_cost_1d\r\nwhere dt >= date_add(\'2025-08-07\', -2) and dt <= \'2025-08-07\'\r\nand short_play_id is not null\r\ngroup by dt, short_play_code, app_language\r\n)\r\ninsert overwrite ads.ads_bi_ad_short_cost_info_1d partition (dt)\r\nselect\r\na.short_play_code as short_play_code,\r\na.app_language,\r\nb.short_play_name,\r\nb.short_play_name_alias,\r\nb.short_play_type,\r\ncast(b.language_name as string) as language_name,\r\nb.pub_time,\r\na.cost_amt as cost_amt,\r\na.viewers_unt,\r\na.short_play_one_code,\r\na.viewers_unt_one,\r\na.short_play_two_code,\r\na.viewers_unt_two,\r\na.short_play_three_code,\r\na.viewers_unt_three,\r\na.etl_tm,\r\na.dt\r\nfrom (\r\nselect\r\ncoalesce(t.short_play_code, t1.short_play_code, t2.short_play_code, \'\') as short_play_code,\r\ncoalesce(t.app_language, t1.app_language, t2.app_language, \'\') as app_language,\r\ncoalesce(tt.dt, t.dt, t1.dt, t2.dt) as dt,\r\nsum(nvl(t2.cost_amt, 0)) as cost_amt,\r\nsum(nvl(t.total_viewers_unt, 0)) as viewers_unt,\r\nmax(t1.short_play_one_code) as short_play_one_code,\r\nsum(nvl(t1.viewers_unt_one, 0)) as viewers_unt_one,\r\nmax(t1.short_play_two_code) as short_play_two_code,\r\nsum(nvl(t1.viewers_unt_two, 0)) as viewers_unt_two,\r\nmax(t1.short_play_three_code) as short_play_three_code,\r\nsum(nvl(t1.viewers_unt_three, 0)) as viewers_unt_three,\r\ncurrent_timestamp() as etl_tm\r\nfrom (\r\nselect date(\'2025-08-07\') as dt, short_play_code as short_play_code from dim.dim_short_play_id_info\r\nunion all\r\nselect date(date_add(\'2025-08-07\', -1)) as dt, short_play_code as short_play_code from dim.dim_short_play_id_info\r\nunion all\r\nselect date(date_add(\'2025-08-07\', -2)) as dt, short_play_code as short_play_code from dim.dim_short_play_id_info\r\n) tt\r\nfull join total_short_viewers_tmp t on tt.short_play_code = t.short_play_code and tt.dt = t.dt\r\nfull join flow_code_user_unt t1 on tt.short_play_code = t1.short_play_code and t.app_language = t1.app_language and tt.dt = t1.dt\r\nfull join ad_cost_amt_tmp t2 on tt.short_play_code = t2.short_play_code and t.app_language = t2.app_language and tt.dt = t2.dt\r\ngroup by\r\ncoalesce(t.short_play_code, t1.short_play_code, t2.short_play_code, \'\'),\r\ncoalesce(t.app_language, t1.app_language, t2.app_language, \'\'),\r\ncoalesce(tt.dt, t.dt, t1.dt, t2.dt)\r\n) a\r\nleft join dim.dim_short_play_id_info b on a.short_play_code = b.short_play_code\r\nwhere trim(a.short_play_code) <> \'\'\r\n']

[INFO] 2025-08-09 16:32:01.865 +0800 -  -> 

	2025-08-09 16:32:00,924 - INFO - ---> 25/08/09 16:32:00 WARN DependencyUtils: Skip remote jar hdfs://shffff/dataware/jars/spark/SparkProject.jar.

	2025-08-09 16:32:01,810 - INFO - ---> 25/08/09 16:32:01 INFO Configuration: found resource resource-types.xml at file:/etc/hadoop/*******-007/0/resource-types.xml

	2025-08-09 16:32:01,830 - INFO - ---> 25/08/09 16:32:01 INFO Client: Verifying our application has not requested more than the maximum memory capability of the cluster (446464 MB per container)

	2025-08-09 16:32:01,831 - INFO - ---> 25/08/09 16:32:01 INFO Client: Will allocate AM container, with 5632 MB memory including 512 MB overhead

	2025-08-09 16:32:01,831 - INFO - ---> 25/08/09 16:32:01 INFO Client: Setting up container launch context for our AM

	2025-08-09 16:32:01,832 - INFO - ---> 25/08/09 16:32:01 INFO Client: Setting up the launch environment for our AM container

	2025-08-09 16:32:01,845 - INFO - ---> 25/08/09 16:32:01 INFO Client: Preparing resources for our AM container

[INFO] 2025-08-09 16:32:02.865 +0800 -  -> 

	2025-08-09 16:32:01,974 - INFO - ---> 25/08/09 16:32:01 INFO Client: Source and destination file systems are the same. Not copying hdfs:/hdp/apps/*******-007/spark3/spark3-hdp-yarn-archive.tar.gz

	2025-08-09 16:32:02,056 - INFO - ---> 25/08/09 16:32:02 INFO Client: Source and destination file systems are the same. Not copying hdfs://shffff/dataware/jars/spark/SparkProject.jar

	2025-08-09 16:32:02,205 - INFO - ---> 25/08/09 16:32:02 INFO Client: Uploading resource file:/tmp/spark-b31ef5fc-7404-4282-b0ca-08675cd406c9/__spark_conf__4069178735143182185.zip -> hdfs://shffff/user/root/.sparkStaging/application_1752723821416_15504/__spark_conf__.zip

	2025-08-09 16:32:02,422 - INFO - ---> 25/08/09 16:32:02 WARN Client: spark.yarn.am.extraJavaOptions will not take effect in cluster mode

	2025-08-09 16:32:02,443 - INFO - ---> 25/08/09 16:32:02 INFO SecurityManager: Changing view acls to: root

	2025-08-09 16:32:02,443 - INFO - ---> 25/08/09 16:32:02 INFO SecurityManager: Changing modify acls to: root

	2025-08-09 16:32:02,444 - INFO - ---> 25/08/09 16:32:02 INFO SecurityManager: Changing view acls groups to: 

	2025-08-09 16:32:02,444 - INFO - ---> 25/08/09 16:32:02 INFO SecurityManager: Changing modify acls groups to: 

	2025-08-09 16:32:02,444 - INFO - ---> 25/08/09 16:32:02 INFO SecurityManager: SecurityManager: authentication disabled; ui acls disabled; users with view permissions: root; groups with view permissions: EMPTY; users with modify permissions: root; groups with modify permissions: EMPTY

	2025-08-09 16:32:02,495 - INFO - ---> 25/08/09 16:32:02 INFO Client: Submitting application application_1752723821416_15504 to ResourceManager

	2025-08-09 16:32:02,533 - INFO - ---> 25/08/09 16:32:02 INFO YarnClientImpl: Submitted application application_1752723821416_15504

[INFO] 2025-08-09 16:32:03.866 +0800 -  -> 

	2025-08-09 16:32:03,537 - INFO - ---> 25/08/09 16:32:03 INFO Client: Application report for application_1752723821416_15504 (state: ACCEPTED)

	2025-08-09 16:32:03,543 - INFO - ---> 25/08/09 16:32:03 INFO Client: 

	2025-08-09 16:32:03,543 - INFO - ---> 	 client token: N/A

	2025-08-09 16:32:03,543 - INFO - ---> 	 diagnostics: AM container is launched, waiting for AM container to Register with RM

	2025-08-09 16:32:03,543 - INFO - ---> 	 ApplicationMaster host: N/A

	2025-08-09 16:32:03,543 - INFO - ---> 	 ApplicationMaster RPC port: -1

	2025-08-09 16:32:03,543 - INFO - ---> 	 queue: pro

	2025-08-09 16:32:03,544 - INFO - ---> 	 start time: 1754728322508

	2025-08-09 16:32:03,544 - INFO - ---> 	 final status: UNDEFINED

	2025-08-09 16:32:03,544 - INFO - ---> 	 tracking URL: http://bigdata-6-1:8088/proxy/application_1752723821416_15504/

	2025-08-09 16:32:03,544 - INFO - ---> 	 user: root

[INFO] 2025-08-09 16:32:04.867 +0800 -  -> 

	2025-08-09 16:32:04,545 - INFO - ---> 25/08/09 16:32:04 INFO Client: Application report for application_1752723821416_15504 (state: ACCEPTED)

[INFO] 2025-08-09 16:32:05.867 +0800 -  -> 

	2025-08-09 16:32:05,548 - INFO - ---> 25/08/09 16:32:05 INFO Client: Application report for application_1752723821416_15504 (state: ACCEPTED)

[INFO] 2025-08-09 16:32:06.868 +0800 -  -> 

	2025-08-09 16:32:06,551 - INFO - ---> 25/08/09 16:32:06 INFO Client: Application report for application_1752723821416_15504 (state: ACCEPTED)

[INFO] 2025-08-09 16:32:07.868 +0800 -  -> 

	2025-08-09 16:32:07,553 - INFO - ---> 25/08/09 16:32:07 INFO Client: Application report for application_1752723821416_15504 (state: ACCEPTED)

[INFO] 2025-08-09 16:32:08.869 +0800 -  -> 

	2025-08-09 16:32:08,556 - INFO - ---> 25/08/09 16:32:08 INFO Client: Application report for application_1752723821416_15504 (state: ACCEPTED)

[INFO] 2025-08-09 16:32:09.869 +0800 -  -> 

	2025-08-09 16:32:09,558 - INFO - ---> 25/08/09 16:32:09 INFO Client: Application report for application_1752723821416_15504 (state: RUNNING)

	2025-08-09 16:32:09,559 - INFO - ---> 25/08/09 16:32:09 INFO Client: 

	2025-08-09 16:32:09,559 - INFO - ---> 	 client token: N/A

	2025-08-09 16:32:09,559 - INFO - ---> 	 diagnostics: N/A

	2025-08-09 16:32:09,559 - INFO - ---> 	 ApplicationMaster host: dn-6-12

	2025-08-09 16:32:09,560 - INFO - ---> 	 ApplicationMaster RPC port: 43212

	2025-08-09 16:32:09,560 - INFO - ---> 	 queue: pro

	2025-08-09 16:32:09,560 - INFO - ---> 	 start time: 1754728322508

	2025-08-09 16:32:09,560 - INFO - ---> 	 final status: UNDEFINED

	2025-08-09 16:32:09,560 - INFO - ---> 	 tracking URL: http://bigdata-6-1:8088/proxy/application_1752723821416_15504/

	2025-08-09 16:32:09,560 - INFO - ---> 	 user: root

[INFO] 2025-08-09 16:32:10.870 +0800 -  -> 

	2025-08-09 16:32:10,561 - INFO - ---> 25/08/09 16:32:10 INFO Client: Application report for application_1752723821416_15504 (state: RUNNING)

[INFO] 2025-08-09 16:32:11.870 +0800 -  -> 

	2025-08-09 16:32:11,564 - INFO - ---> 25/08/09 16:32:11 INFO Client: Application report for application_1752723821416_15504 (state: RUNNING)

[INFO] 2025-08-09 16:32:12.870 +0800 -  -> 

	2025-08-09 16:32:12,566 - INFO - ---> 25/08/09 16:32:12 INFO Client: Application report for application_1752723821416_15504 (state: RUNNING)

[INFO] 2025-08-09 16:32:13.871 +0800 -  -> 

	2025-08-09 16:32:13,569 - INFO - ---> 25/08/09 16:32:13 INFO Client: Application report for application_1752723821416_15504 (state: RUNNING)

[INFO] 2025-08-09 16:32:14.871 +0800 -  -> 

	2025-08-09 16:32:14,571 - INFO - ---> 25/08/09 16:32:14 INFO Client: Application report for application_1752723821416_15504 (state: RUNNING)

[INFO] 2025-08-09 16:32:15.872 +0800 -  -> 

	2025-08-09 16:32:15,574 - INFO - ---> 25/08/09 16:32:15 INFO Client: Application report for application_1752723821416_15504 (state: RUNNING)

[INFO] 2025-08-09 16:32:16.872 +0800 -  -> 

	2025-08-09 16:32:16,577 - INFO - ---> 25/08/09 16:32:16 INFO Client: Application report for application_1752723821416_15504 (state: RUNNING)

[INFO] 2025-08-09 16:32:17.872 +0800 -  -> 

	2025-08-09 16:32:17,580 - INFO - ---> 25/08/09 16:32:17 INFO Client: Application report for application_1752723821416_15504 (state: RUNNING)

[INFO] 2025-08-09 16:32:18.873 +0800 -  -> 

	2025-08-09 16:32:18,583 - INFO - ---> 25/08/09 16:32:18 INFO Client: Application report for application_1752723821416_15504 (state: RUNNING)

[INFO] 2025-08-09 16:32:19.873 +0800 -  -> 

	2025-08-09 16:32:19,585 - INFO - ---> 25/08/09 16:32:19 INFO Client: Application report for application_1752723821416_15504 (state: RUNNING)

[INFO] 2025-08-09 16:32:20.874 +0800 -  -> 

	2025-08-09 16:32:20,588 - INFO - ---> 25/08/09 16:32:20 INFO Client: Application report for application_1752723821416_15504 (state: RUNNING)

[INFO] 2025-08-09 16:32:21.874 +0800 -  -> 

	2025-08-09 16:32:21,590 - INFO - ---> 25/08/09 16:32:21 INFO Client: Application report for application_1752723821416_15504 (state: RUNNING)

[INFO] 2025-08-09 16:32:22.874 +0800 -  -> 

	2025-08-09 16:32:22,593 - INFO - ---> 25/08/09 16:32:22 INFO Client: Application report for application_1752723821416_15504 (state: RUNNING)

[INFO] 2025-08-09 16:32:23.875 +0800 -  -> 

	2025-08-09 16:32:23,595 - INFO - ---> 25/08/09 16:32:23 INFO Client: Application report for application_1752723821416_15504 (state: RUNNING)

[INFO] 2025-08-09 16:32:24.875 +0800 -  -> 

	2025-08-09 16:32:24,597 - INFO - ---> 25/08/09 16:32:24 INFO Client: Application report for application_1752723821416_15504 (state: RUNNING)

[INFO] 2025-08-09 16:32:25.876 +0800 -  -> 

	2025-08-09 16:32:25,600 - INFO - ---> 25/08/09 16:32:25 INFO Client: Application report for application_1752723821416_15504 (state: RUNNING)

[INFO] 2025-08-09 16:32:26.876 +0800 -  -> 

	2025-08-09 16:32:26,602 - INFO - ---> 25/08/09 16:32:26 INFO Client: Application report for application_1752723821416_15504 (state: RUNNING)

[INFO] 2025-08-09 16:32:27.877 +0800 -  -> 

	2025-08-09 16:32:27,604 - INFO - ---> 25/08/09 16:32:27 INFO Client: Application report for application_1752723821416_15504 (state: RUNNING)

[INFO] 2025-08-09 16:32:28.877 +0800 -  -> 

	2025-08-09 16:32:28,607 - INFO - ---> 25/08/09 16:32:28 INFO Client: Application report for application_1752723821416_15504 (state: RUNNING)

[INFO] 2025-08-09 16:32:29.877 +0800 -  -> 

	2025-08-09 16:32:29,609 - INFO - ---> 25/08/09 16:32:29 INFO Client: Application report for application_1752723821416_15504 (state: RUNNING)

[INFO] 2025-08-09 16:32:30.878 +0800 -  -> 

	2025-08-09 16:32:30,611 - INFO - ---> 25/08/09 16:32:30 INFO Client: Application report for application_1752723821416_15504 (state: RUNNING)

[INFO] 2025-08-09 16:32:31.878 +0800 -  -> 

	2025-08-09 16:32:31,613 - INFO - ---> 25/08/09 16:32:31 INFO Client: Application report for application_1752723821416_15504 (state: RUNNING)

[INFO] 2025-08-09 16:32:32.879 +0800 -  -> 

	2025-08-09 16:32:32,616 - INFO - ---> 25/08/09 16:32:32 INFO Client: Application report for application_1752723821416_15504 (state: RUNNING)

[INFO] 2025-08-09 16:32:33.879 +0800 -  -> 

	2025-08-09 16:32:33,618 - INFO - ---> 25/08/09 16:32:33 INFO Client: Application report for application_1752723821416_15504 (state: RUNNING)

[INFO] 2025-08-09 16:32:34.880 +0800 -  -> 

	2025-08-09 16:32:34,620 - INFO - ---> 25/08/09 16:32:34 INFO Client: Application report for application_1752723821416_15504 (state: RUNNING)

[INFO] 2025-08-09 16:32:35.880 +0800 -  -> 

	2025-08-09 16:32:35,623 - INFO - ---> 25/08/09 16:32:35 INFO Client: Application report for application_1752723821416_15504 (state: RUNNING)

[INFO] 2025-08-09 16:32:36.880 +0800 -  -> 

	2025-08-09 16:32:36,625 - INFO - ---> 25/08/09 16:32:36 INFO Client: Application report for application_1752723821416_15504 (state: RUNNING)

[INFO] 2025-08-09 16:32:37.881 +0800 -  -> 

	2025-08-09 16:32:37,627 - INFO - ---> 25/08/09 16:32:37 INFO Client: Application report for application_1752723821416_15504 (state: RUNNING)

[INFO] 2025-08-09 16:32:38.881 +0800 -  -> 

	2025-08-09 16:32:38,630 - INFO - ---> 25/08/09 16:32:38 INFO Client: Application report for application_1752723821416_15504 (state: RUNNING)

[INFO] 2025-08-09 16:32:39.882 +0800 -  -> 

	2025-08-09 16:32:39,632 - INFO - ---> 25/08/09 16:32:39 INFO Client: Application report for application_1752723821416_15504 (state: RUNNING)

[INFO] 2025-08-09 16:32:40.882 +0800 -  -> 

	2025-08-09 16:32:40,634 - INFO - ---> 25/08/09 16:32:40 INFO Client: Application report for application_1752723821416_15504 (state: RUNNING)

[INFO] 2025-08-09 16:32:41.882 +0800 -  -> 

	2025-08-09 16:32:41,636 - INFO - ---> 25/08/09 16:32:41 INFO Client: Application report for application_1752723821416_15504 (state: RUNNING)

[INFO] 2025-08-09 16:32:42.883 +0800 -  -> 

	2025-08-09 16:32:42,638 - INFO - ---> 25/08/09 16:32:42 INFO Client: Application report for application_1752723821416_15504 (state: RUNNING)

[INFO] 2025-08-09 16:32:43.883 +0800 -  -> 

	2025-08-09 16:32:43,641 - INFO - ---> 25/08/09 16:32:43 INFO Client: Application report for application_1752723821416_15504 (state: RUNNING)

[INFO] 2025-08-09 16:32:44.884 +0800 -  -> 

	2025-08-09 16:32:44,643 - INFO - ---> 25/08/09 16:32:44 INFO Client: Application report for application_1752723821416_15504 (state: RUNNING)

[INFO] 2025-08-09 16:32:45.884 +0800 -  -> 

	2025-08-09 16:32:45,645 - INFO - ---> 25/08/09 16:32:45 INFO Client: Application report for application_1752723821416_15504 (state: RUNNING)

[INFO] 2025-08-09 16:32:46.884 +0800 -  -> 

	2025-08-09 16:32:46,647 - INFO - ---> 25/08/09 16:32:46 INFO Client: Application report for application_1752723821416_15504 (state: RUNNING)

[INFO] 2025-08-09 16:32:47.885 +0800 -  -> 

	2025-08-09 16:32:47,649 - INFO - ---> 25/08/09 16:32:47 INFO Client: Application report for application_1752723821416_15504 (state: RUNNING)

[INFO] 2025-08-09 16:32:48.885 +0800 -  -> 

	2025-08-09 16:32:48,651 - INFO - ---> 25/08/09 16:32:48 INFO Client: Application report for application_1752723821416_15504 (state: ACCEPTED)

	2025-08-09 16:32:48,652 - INFO - ---> 25/08/09 16:32:48 INFO Client: 

	2025-08-09 16:32:48,652 - INFO - ---> 	 client token: N/A

	2025-08-09 16:32:48,652 - INFO - ---> 	 diagnostics: AM container is launched, waiting for AM container to Register with RM

	2025-08-09 16:32:48,652 - INFO - ---> 	 ApplicationMaster host: N/A

	2025-08-09 16:32:48,652 - INFO - ---> 	 ApplicationMaster RPC port: -1

	2025-08-09 16:32:48,652 - INFO - ---> 	 queue: pro

	2025-08-09 16:32:48,653 - INFO - ---> 	 start time: 1754728322508

	2025-08-09 16:32:48,653 - INFO - ---> 	 final status: UNDEFINED

	2025-08-09 16:32:48,653 - INFO - ---> 	 tracking URL: http://bigdata-6-1:8088/proxy/application_1752723821416_15504/

	2025-08-09 16:32:48,653 - INFO - ---> 	 user: root

[INFO] 2025-08-09 16:32:49.886 +0800 -  -> 

	2025-08-09 16:32:49,654 - INFO - ---> 25/08/09 16:32:49 INFO Client: Application report for application_1752723821416_15504 (state: ACCEPTED)

[INFO] 2025-08-09 16:32:50.886 +0800 -  -> 

	2025-08-09 16:32:50,656 - INFO - ---> 25/08/09 16:32:50 INFO Client: Application report for application_1752723821416_15504 (state: ACCEPTED)

[INFO] 2025-08-09 16:32:51.887 +0800 -  -> 

	2025-08-09 16:32:51,658 - INFO - ---> 25/08/09 16:32:51 INFO Client: Application report for application_1752723821416_15504 (state: ACCEPTED)

[INFO] 2025-08-09 16:32:52.887 +0800 -  -> 

	2025-08-09 16:32:52,660 - INFO - ---> 25/08/09 16:32:52 INFO Client: Application report for application_1752723821416_15504 (state: ACCEPTED)

[INFO] 2025-08-09 16:32:53.888 +0800 -  -> 

	2025-08-09 16:32:53,662 - INFO - ---> 25/08/09 16:32:53 INFO Client: Application report for application_1752723821416_15504 (state: RUNNING)

	2025-08-09 16:32:53,663 - INFO - ---> 25/08/09 16:32:53 INFO Client: 

	2025-08-09 16:32:53,663 - INFO - ---> 	 client token: N/A

	2025-08-09 16:32:53,663 - INFO - ---> 	 diagnostics: N/A

	2025-08-09 16:32:53,663 - INFO - ---> 	 ApplicationMaster host: dn-6-9

	2025-08-09 16:32:53,663 - INFO - ---> 	 ApplicationMaster RPC port: 36259

	2025-08-09 16:32:53,663 - INFO - ---> 	 queue: pro

	2025-08-09 16:32:53,663 - INFO - ---> 	 start time: 1754728322508

	2025-08-09 16:32:53,663 - INFO - ---> 	 final status: UNDEFINED

	2025-08-09 16:32:53,664 - INFO - ---> 	 tracking URL: http://bigdata-6-1:8088/proxy/application_1752723821416_15504/

	2025-08-09 16:32:53,664 - INFO - ---> 	 user: root

[INFO] 2025-08-09 16:32:54.888 +0800 -  -> 

	2025-08-09 16:32:54,665 - INFO - ---> 25/08/09 16:32:54 INFO Client: Application report for application_1752723821416_15504 (state: RUNNING)

[INFO] 2025-08-09 16:32:55.888 +0800 -  -> 

	2025-08-09 16:32:55,667 - INFO - ---> 25/08/09 16:32:55 INFO Client: Application report for application_1752723821416_15504 (state: RUNNING)

[INFO] 2025-08-09 16:32:56.889 +0800 -  -> 

	2025-08-09 16:32:56,669 - INFO - ---> 25/08/09 16:32:56 INFO Client: Application report for application_1752723821416_15504 (state: RUNNING)

[INFO] 2025-08-09 16:32:57.889 +0800 -  -> 

	2025-08-09 16:32:57,672 - INFO - ---> 25/08/09 16:32:57 INFO Client: Application report for application_1752723821416_15504 (state: RUNNING)

[INFO] 2025-08-09 16:32:58.890 +0800 -  -> 

	2025-08-09 16:32:58,674 - INFO - ---> 25/08/09 16:32:58 INFO Client: Application report for application_1752723821416_15504 (state: RUNNING)

[INFO] 2025-08-09 16:32:59.890 +0800 -  -> 

	2025-08-09 16:32:59,676 - INFO - ---> 25/08/09 16:32:59 INFO Client: Application report for application_1752723821416_15504 (state: RUNNING)

[INFO] 2025-08-09 16:33:00.891 +0800 -  -> 

	2025-08-09 16:33:00,678 - INFO - ---> 25/08/09 16:33:00 INFO Client: Application report for application_1752723821416_15504 (state: RUNNING)

[INFO] 2025-08-09 16:33:01.891 +0800 -  -> 

	2025-08-09 16:33:01,680 - INFO - ---> 25/08/09 16:33:01 INFO Client: Application report for application_1752723821416_15504 (state: RUNNING)

[INFO] 2025-08-09 16:33:02.892 +0800 -  -> 

	2025-08-09 16:33:02,682 - INFO - ---> 25/08/09 16:33:02 INFO Client: Application report for application_1752723821416_15504 (state: RUNNING)

[INFO] 2025-08-09 16:33:03.892 +0800 -  -> 

	2025-08-09 16:33:03,684 - INFO - ---> 25/08/09 16:33:03 INFO Client: Application report for application_1752723821416_15504 (state: RUNNING)

[INFO] 2025-08-09 16:33:04.892 +0800 -  -> 

	2025-08-09 16:33:04,686 - INFO - ---> 25/08/09 16:33:04 INFO Client: Application report for application_1752723821416_15504 (state: RUNNING)

[INFO] 2025-08-09 16:33:05.893 +0800 -  -> 

	2025-08-09 16:33:05,688 - INFO - ---> 25/08/09 16:33:05 INFO Client: Application report for application_1752723821416_15504 (state: RUNNING)

[INFO] 2025-08-09 16:33:06.893 +0800 -  -> 

	2025-08-09 16:33:06,690 - INFO - ---> 25/08/09 16:33:06 INFO Client: Application report for application_1752723821416_15504 (state: RUNNING)

[INFO] 2025-08-09 16:33:07.894 +0800 -  -> 

	2025-08-09 16:33:07,692 - INFO - ---> 25/08/09 16:33:07 INFO Client: Application report for application_1752723821416_15504 (state: RUNNING)

[INFO] 2025-08-09 16:33:08.894 +0800 -  -> 

	2025-08-09 16:33:08,694 - INFO - ---> 25/08/09 16:33:08 INFO Client: Application report for application_1752723821416_15504 (state: RUNNING)

[INFO] 2025-08-09 16:33:09.895 +0800 -  -> 

	2025-08-09 16:33:09,696 - INFO - ---> 25/08/09 16:33:09 INFO Client: Application report for application_1752723821416_15504 (state: RUNNING)

[INFO] 2025-08-09 16:33:10.895 +0800 -  -> 

	2025-08-09 16:33:10,698 - INFO - ---> 25/08/09 16:33:10 INFO Client: Application report for application_1752723821416_15504 (state: RUNNING)

[INFO] 2025-08-09 16:33:11.895 +0800 -  -> 

	2025-08-09 16:33:11,700 - INFO - ---> 25/08/09 16:33:11 INFO Client: Application report for application_1752723821416_15504 (state: RUNNING)

[INFO] 2025-08-09 16:33:12.896 +0800 -  -> 

	2025-08-09 16:33:12,702 - INFO - ---> 25/08/09 16:33:12 INFO Client: Application report for application_1752723821416_15504 (state: RUNNING)

[INFO] 2025-08-09 16:33:13.896 +0800 -  -> 

	2025-08-09 16:33:13,704 - INFO - ---> 25/08/09 16:33:13 INFO Client: Application report for application_1752723821416_15504 (state: RUNNING)

[INFO] 2025-08-09 16:33:14.897 +0800 -  -> 

	2025-08-09 16:33:14,705 - INFO - ---> 25/08/09 16:33:14 INFO Client: Application report for application_1752723821416_15504 (state: RUNNING)

[INFO] 2025-08-09 16:33:15.897 +0800 -  -> 

	2025-08-09 16:33:15,707 - INFO - ---> 25/08/09 16:33:15 INFO Client: Application report for application_1752723821416_15504 (state: RUNNING)

[INFO] 2025-08-09 16:33:16.898 +0800 -  -> 

	2025-08-09 16:33:16,709 - INFO - ---> 25/08/09 16:33:16 INFO Client: Application report for application_1752723821416_15504 (state: RUNNING)

[INFO] 2025-08-09 16:33:17.898 +0800 -  -> 

	2025-08-09 16:33:17,711 - INFO - ---> 25/08/09 16:33:17 INFO Client: Application report for application_1752723821416_15504 (state: RUNNING)

[INFO] 2025-08-09 16:33:18.898 +0800 -  -> 

	2025-08-09 16:33:18,713 - INFO - ---> 25/08/09 16:33:18 INFO Client: Application report for application_1752723821416_15504 (state: RUNNING)

[INFO] 2025-08-09 16:33:19.899 +0800 -  -> 

	2025-08-09 16:33:19,715 - INFO - ---> 25/08/09 16:33:19 INFO Client: Application report for application_1752723821416_15504 (state: RUNNING)

[INFO] 2025-08-09 16:33:20.899 +0800 -  -> 

	2025-08-09 16:33:20,717 - INFO - ---> 25/08/09 16:33:20 INFO Client: Application report for application_1752723821416_15504 (state: RUNNING)

[INFO] 2025-08-09 16:33:21.900 +0800 -  -> 

	2025-08-09 16:33:21,719 - INFO - ---> 25/08/09 16:33:21 INFO Client: Application report for application_1752723821416_15504 (state: RUNNING)

[INFO] 2025-08-09 16:33:22.900 +0800 -  -> 

	2025-08-09 16:33:22,721 - INFO - ---> 25/08/09 16:33:22 INFO Client: Application report for application_1752723821416_15504 (state: RUNNING)

[INFO] 2025-08-09 16:33:23.901 +0800 -  -> 

	2025-08-09 16:33:23,723 - INFO - ---> 25/08/09 16:33:23 INFO Client: Application report for application_1752723821416_15504 (state: RUNNING)

[INFO] 2025-08-09 16:33:24.901 +0800 -  -> 

	2025-08-09 16:33:24,725 - INFO - ---> 25/08/09 16:33:24 INFO Client: Application report for application_1752723821416_15504 (state: RUNNING)

[INFO] 2025-08-09 16:33:25.901 +0800 -  -> 

	2025-08-09 16:33:25,727 - INFO - ---> 25/08/09 16:33:25 INFO Client: Application report for application_1752723821416_15504 (state: RUNNING)

[INFO] 2025-08-09 16:33:26.902 +0800 -  -> 

	2025-08-09 16:33:26,729 - INFO - ---> 25/08/09 16:33:26 INFO Client: Application report for application_1752723821416_15504 (state: RUNNING)

[INFO] 2025-08-09 16:33:27.902 +0800 -  -> 

	2025-08-09 16:33:27,731 - INFO - ---> 25/08/09 16:33:27 INFO Client: Application report for application_1752723821416_15504 (state: RUNNING)

[INFO] 2025-08-09 16:33:28.903 +0800 -  -> 

	2025-08-09 16:33:28,733 - INFO - ---> 25/08/09 16:33:28 INFO Client: Application report for application_1752723821416_15504 (state: RUNNING)

[INFO] 2025-08-09 16:33:29.903 +0800 -  -> 

	2025-08-09 16:33:29,735 - INFO - ---> 25/08/09 16:33:29 INFO Client: Application report for application_1752723821416_15504 (state: RUNNING)

[INFO] 2025-08-09 16:33:30.904 +0800 -  -> 

	2025-08-09 16:33:30,737 - INFO - ---> 25/08/09 16:33:30 INFO Client: Application report for application_1752723821416_15504 (state: RUNNING)

[INFO] 2025-08-09 16:33:31.904 +0800 -  -> 

	2025-08-09 16:33:31,739 - INFO - ---> 25/08/09 16:33:31 INFO Client: Application report for application_1752723821416_15504 (state: RUNNING)

[INFO] 2025-08-09 16:33:32.904 +0800 -  -> 

	2025-08-09 16:33:32,741 - INFO - ---> 25/08/09 16:33:32 INFO Client: Application report for application_1752723821416_15504 (state: FINISHED)

	2025-08-09 16:33:32,742 - INFO - ---> 25/08/09 16:33:32 INFO Client: 

	2025-08-09 16:33:32,742 - INFO - ---> 	 client token: N/A

	2025-08-09 16:33:32,742 - INFO - ---> 	 diagnostics: User class threw exception: java.lang.NoSuchMethodException: org.apache.hadoop.hive.ql.metadata.Hive.loadDynamicPartitions(org.apache.hadoop.fs.Path, java.lang.String, java.util.Map, boolean, int, boolean, boolean, boolean, long)

	2025-08-09 16:33:32,742 - INFO - ---> 	at java.lang.Class.getMethod(Class.java:1786)

	2025-08-09 16:33:32,743 - INFO - ---> 	at org.apache.spark.sql.hive.client.Shim.findMethod(HiveShim.scala:249)

	2025-08-09 16:33:32,743 - INFO - ---> 	at org.apache.spark.sql.hive.client.Shim_v1_2.loadDynamicPartitionsMethod$lzycompute(HiveShim.scala:1385)

	2025-08-09 16:33:32,743 - INFO - ---> 	at org.apache.spark.sql.hive.client.Shim_v1_2.loadDynamicPartitionsMethod(HiveShim.scala:1373)

	2025-08-09 16:33:32,743 - INFO - ---> 	at org.apache.spark.sql.hive.client.Shim_v1_2.loadDynamicPartitions(HiveShim.scala:1409)

	2025-08-09 16:33:32,743 - INFO - ---> 	at org.apache.spark.sql.hive.client.HiveClientImpl.$anonfun$loadDynamicPartitions$1(HiveClientImpl.scala:977)

	2025-08-09 16:33:32,743 - INFO - ---> 	at scala.runtime.java8.JFunction0$mcV$sp.apply(JFunction0$mcV$sp.java:23)

	2025-08-09 16:33:32,743 - INFO - ---> 	at org.apache.spark.sql.hive.client.HiveClientImpl.$anonfun$withHiveState$1(HiveClientImpl.scala:303)

	2025-08-09 16:33:32,743 - INFO - ---> 	at org.apache.spark.sql.hive.client.HiveClientImpl.liftedTree1$1(HiveClientImpl.scala:234)

	2025-08-09 16:33:32,744 - INFO - ---> 	at org.apache.spark.sql.hive.client.HiveClientImpl.retryLocked(HiveClientImpl.scala:233)

	2025-08-09 16:33:32,744 - INFO - ---> 	at org.apache.spark.sql.hive.client.HiveClientImpl.withHiveState(HiveClientImpl.scala:283)

	2025-08-09 16:33:32,744 - INFO - ---> 	at org.apache.spark.sql.hive.client.HiveClientImpl.loadDynamicPartitions(HiveClientImpl.scala:968)

	2025-08-09 16:33:32,744 - INFO - ---> 	at org.apache.spark.sql.hive.HiveExternalCatalog.$anonfun$loadDynamicPartitions$1(HiveExternalCatalog.scala:966)

	2025-08-09 16:33:32,744 - INFO - ---> 	at scala.runtime.java8.JFunction0$mcV$sp.apply(JFunction0$mcV$sp.java:23)

	2025-08-09 16:33:32,744 - INFO - ---> 	at org.apache.spark.sql.hive.HiveExternalCatalog.withClient(HiveExternalCatalog.scala:101)

	2025-08-09 16:33:32,744 - INFO - ---> 	at org.apache.spark.sql.hive.HiveExternalCatalog.loadDynamicPartitions(HiveExternalCatalog.scala:946)

	2025-08-09 16:33:32,744 - INFO - ---> 	at org.apache.spark.sql.catalyst.catalog.ExternalCatalogWithListener.loadDynamicPartitions(ExternalCatalogWithListener.scala:189)

	2025-08-09 16:33:32,745 - INFO - ---> 	at org.apache.spark.sql.hive.execution.InsertIntoHiveTable.processInsert(InsertIntoHiveTable.scala:200)

	2025-08-09 16:33:32,745 - INFO - ---> 	at org.apache.spark.sql.hive.execution.InsertIntoHiveTable.run(InsertIntoHiveTable.scala:105)

	2025-08-09 16:33:32,745 - INFO - ---> 	at org.apache.spark.sql.execution.command.DataWritingCommandExec.sideEffectResult$lzycompute(commands.scala:113)

	2025-08-09 16:33:32,745 - INFO - ---> 	at org.apache.spark.sql.execution.command.DataWritingCommandExec.sideEffectResult(commands.scala:111)

	2025-08-09 16:33:32,745 - INFO - ---> 	at org.apache.spark.sql.execution.command.DataWritingCommandExec.executeCollect(commands.scala:125)

	2025-08-09 16:33:32,745 - INFO - ---> 	at org.apache.spark.sql.execution.adaptive.AdaptiveSparkPlanExec.$anonfun$executeCollect$1(AdaptiveSparkPlanExec.scala:360)

	2025-08-09 16:33:32,745 - INFO - ---> 	at org.apache.spark.sql.execution.adaptive.AdaptiveSparkPlanExec.withFinalPlanUpdate(AdaptiveSparkPlanExec.scala:388)

	2025-08-09 16:33:32,745 - INFO - ---> 	at org.apache.spark.sql.execution.adaptive.AdaptiveSparkPlanExec.executeCollect(AdaptiveSparkPlanExec.scala:360)

	2025-08-09 16:33:32,745 - INFO - ---> 	at org.apache.spark.sql.execution.QueryExecution$$anonfun$eagerlyExecuteCommands$1.$anonfun$applyOrElse$1(QueryExecution.scala:98)

	2025-08-09 16:33:32,746 - INFO - ---> 	at org.apache.spark.sql.execution.SQLExecution$.$anonfun$withNewExecutionId$6(SQLExecution.scala:118)

	2025-08-09 16:33:32,746 - INFO - ---> 	at org.apache.spark.sql.execution.SQLExecution$.withSQLConfPropagated(SQLExecution.scala:195)

	2025-08-09 16:33:32,746 - INFO - ---> 	at org.apache.spark.sql.execution.SQLExecution$.$anonfun$withNewExecutionId$1(SQLExecution.scala:103)

	2025-08-09 16:33:32,746 - INFO - ---> 	at org.apache.spark.sql.SparkSession.withActive(SparkSession.scala:827)

	2025-08-09 16:33:32,746 - INFO - ---> 	at org.apache.spark.sql.execution.SQLExecution$.withNewExecutionId(SQLExecution.scala:65)

	2025-08-09 16:33:32,746 - INFO - ---> 	at org.apache.spark.sql.execution.QueryExecution$$anonfun$eagerlyExecuteCommands$1.applyOrElse(QueryExecution.scala:98)

	2025-08-09 16:33:32,746 - INFO - ---> 	at org.apache.spark.sql.execution.QueryExecution$$anonfun$eagerlyExecuteCommands$1.applyOrElse(QueryExecution.scala:94)

	2025-08-09 16:33:32,746 - INFO - ---> 	at org.apache.spark.sql.catalyst.trees.TreeNode.$anonfun$transformDownWithPruning$1(TreeNode.scala:512)

	2025-08-09 16:33:32,747 - INFO - ---> 	at org.apache.spark.sql.catalyst.trees.CurrentOrigin$.withOrigin(TreeNode.scala:104)

	2025-08-09 16:33:32,747 - INFO - ---> 	at org.apache.spark.sql.catalyst.trees.TreeNode.transformDownWithPruning(TreeNode.scala:512)

	2025-08-09 16:33:32,747 - INFO - ---> 	at org.apache.spark.sql.catalyst.plans.logical.LogicalPlan.org$apache$spark$sql$catalyst$plans$logical$AnalysisHelper$$super$transformDownWithPruning(LogicalPlan.scala:31)

	2025-08-09 16:33:32,747 - INFO - ---> 	at org.apache.spark.sql.catalyst.plans.logical.AnalysisHelper.transformDownWithPruning(AnalysisHelper.scala:267)

	2025-08-09 16:33:32,747 - INFO - ---> 	at org.apache.spark.sql.catalyst.plans.logical.AnalysisHelper.transformDownWithPruning$(AnalysisHelper.scala:263)

	2025-08-09 16:33:32,747 - INFO - ---> 	at org.apache.spark.sql.catalyst.plans.logical.LogicalPlan.transformDownWithPruning(LogicalPlan.scala:31)

	2025-08-09 16:33:32,747 - INFO - ---> 	at org.apache.spark.sql.catalyst.plans.logical.LogicalPlan.transformDownWithPruning(LogicalPlan.scala:31)

	2025-08-09 16:33:32,747 - INFO - ---> 	at org.apache.spark.sql.catalyst.trees.TreeNode.transformDown(TreeNode.scala:488)

	2025-08-09 16:33:32,748 - INFO - ---> 	at org.apache.spark.sql.execution.QueryExecution.eagerlyExecuteCommands(QueryExecution.scala:94)

	2025-08-09 16:33:32,748 - INFO - ---> 	at org.apache.spark.sql.execution.QueryExecution.commandExecuted$lzycompute(QueryExecution.scala:81)

	2025-08-09 16:33:32,748 - INFO - ---> 	at org.apache.spark.sql.execution.QueryExecution.commandExecuted(QueryExecution.scala:79)

	2025-08-09 16:33:32,748 - INFO - ---> 	at org.apache.spark.sql.Dataset.<init>(Dataset.scala:218)

	2025-08-09 16:33:32,748 - INFO - ---> 	at org.apache.spark.sql.Dataset$.$anonfun$ofRows$2(Dataset.scala:98)

	2025-08-09 16:33:32,748 - INFO - ---> 	at org.apache.spark.sql.SparkSession.withActive(SparkSession.scala:827)

	2025-08-09 16:33:32,748 - INFO - ---> 	at org.apache.spark.sql.Dataset$.ofRows(Dataset.scala:95)

	2025-08-09 16:33:32,748 - INFO - ---> 	at org.apache.spark.sql.SparkSession.$anonfun$sql$1(SparkSession.scala:640)

	2025-08-09 16:33:32,748 - INFO - ---> 	at org.apache.spark.sql.SparkSession.withActive(SparkSession.scala:827)

	2025-08-09 16:33:32,749 - INFO - ---> 	at org.apache.spark.sql.SparkSession.sql(SparkSession.scala:630)

	2025-08-09 16:33:32,749 - INFO - ---> 	at org.apache.spark.sql.SparkSession.sql(SparkSession.scala:671)

	2025-08-09 16:33:32,749 - INFO - ---> 	at com.spark.main.SparkSql.main(SparkSql.java:110)

	2025-08-09 16:33:32,749 - INFO - ---> 	at sun.reflect.NativeMethodAccessorImpl.invoke0(Native Method)

	2025-08-09 16:33:32,749 - INFO - ---> 	at sun.reflect.NativeMethodAccessorImpl.invoke(NativeMethodAccessorImpl.java:62)

	2025-08-09 16:33:32,749 - INFO - ---> 	at sun.reflect.DelegatingMethodAccessorImpl.invoke(DelegatingMethodAccessorImpl.java:43)

	2025-08-09 16:33:32,749 - INFO - ---> 	at java.lang.reflect.Method.invoke(Method.java:498)

	2025-08-09 16:33:32,749 - INFO - ---> 	at org.apache.spark.deploy.yarn.ApplicationMaster$$anon$2.run(ApplicationMaster.scala:757)

	2025-08-09 16:33:32,750 - INFO - ---> 

	2025-08-09 16:33:32,750 - INFO - ---> 	 ApplicationMaster host: dn-6-9

	2025-08-09 16:33:32,750 - INFO - ---> 	 ApplicationMaster RPC port: 36259

	2025-08-09 16:33:32,750 - INFO - ---> 	 queue: pro

	2025-08-09 16:33:32,750 - INFO - ---> 	 start time: 1754728322508

	2025-08-09 16:33:32,750 - INFO - ---> 	 final status: FAILED

	2025-08-09 16:33:32,750 - INFO - ---> 	 tracking URL: http://bigdata-6-1:8088/proxy/application_1752723821416_15504/

	2025-08-09 16:33:32,750 - INFO - ---> 	 user: root

	2025-08-09 16:33:32,753 - INFO - ---> 25/08/09 16:33:32 ERROR Client: Application diagnostics message: User class threw exception: java.lang.NoSuchMethodException: org.apache.hadoop.hive.ql.metadata.Hive.loadDynamicPartitions(org.apache.hadoop.fs.Path, java.lang.String, java.util.Map, boolean, int, boolean, boolean, boolean, long)

	2025-08-09 16:33:32,753 - INFO - ---> 	at java.lang.Class.getMethod(Class.java:1786)

	2025-08-09 16:33:32,754 - INFO - ---> 	at org.apache.spark.sql.hive.client.Shim.findMethod(HiveShim.scala:249)

	2025-08-09 16:33:32,754 - INFO - ---> 	at org.apache.spark.sql.hive.client.Shim_v1_2.loadDynamicPartitionsMethod$lzycompute(HiveShim.scala:1385)

	2025-08-09 16:33:32,754 - INFO - ---> 	at org.apache.spark.sql.hive.client.Shim_v1_2.loadDynamicPartitionsMethod(HiveShim.scala:1373)

	2025-08-09 16:33:32,754 - INFO - ---> 	at org.apache.spark.sql.hive.client.Shim_v1_2.loadDynamicPartitions(HiveShim.scala:1409)

	2025-08-09 16:33:32,754 - INFO - ---> 	at org.apache.spark.sql.hive.client.HiveClientImpl.$anonfun$loadDynamicPartitions$1(HiveClientImpl.scala:977)

	2025-08-09 16:33:32,754 - INFO - ---> 	at scala.runtime.java8.JFunction0$mcV$sp.apply(JFunction0$mcV$sp.java:23)

	2025-08-09 16:33:32,754 - INFO - ---> 	at org.apache.spark.sql.hive.client.HiveClientImpl.$anonfun$withHiveState$1(HiveClientImpl.scala:303)

	2025-08-09 16:33:32,754 - INFO - ---> 	at org.apache.spark.sql.hive.client.HiveClientImpl.liftedTree1$1(HiveClientImpl.scala:234)

	2025-08-09 16:33:32,755 - INFO - ---> 	at org.apache.spark.sql.hive.client.HiveClientImpl.retryLocked(HiveClientImpl.scala:233)

	2025-08-09 16:33:32,755 - INFO - ---> 	at org.apache.spark.sql.hive.client.HiveClientImpl.withHiveState(HiveClientImpl.scala:283)

	2025-08-09 16:33:32,755 - INFO - ---> 	at org.apache.spark.sql.hive.client.HiveClientImpl.loadDynamicPartitions(HiveClientImpl.scala:968)

	2025-08-09 16:33:32,755 - INFO - ---> 	at org.apache.spark.sql.hive.HiveExternalCatalog.$anonfun$loadDynamicPartitions$1(HiveExternalCatalog.scala:966)

	2025-08-09 16:33:32,755 - INFO - ---> 	at scala.runtime.java8.JFunction0$mcV$sp.apply(JFunction0$mcV$sp.java:23)

	2025-08-09 16:33:32,755 - INFO - ---> 	at org.apache.spark.sql.hive.HiveExternalCatalog.withClient(HiveExternalCatalog.scala:101)

	2025-08-09 16:33:32,755 - INFO - ---> 	at org.apache.spark.sql.hive.HiveExternalCatalog.loadDynamicPartitions(HiveExternalCatalog.scala:946)

	2025-08-09 16:33:32,755 - INFO - ---> 	at org.apache.spark.sql.catalyst.catalog.ExternalCatalogWithListener.loadDynamicPartitions(ExternalCatalogWithListener.scala:189)

	2025-08-09 16:33:32,755 - INFO - ---> 	at org.apache.spark.sql.hive.execution.InsertIntoHiveTable.processInsert(InsertIntoHiveTable.scala:200)

	2025-08-09 16:33:32,755 - INFO - ---> 	at org.apache.spark.sql.hive.execution.InsertIntoHiveTable.run(InsertIntoHiveTable.scala:105)

	2025-08-09 16:33:32,756 - INFO - ---> 	at org.apache.spark.sql.execution.command.DataWritingCommandExec.sideEffectResult$lzycompute(commands.scala:113)

	2025-08-09 16:33:32,756 - INFO - ---> 	at org.apache.spark.sql.execution.command.DataWritingCommandExec.sideEffectResult(commands.scala:111)

	2025-08-09 16:33:32,756 - INFO - ---> 	at org.apache.spark.sql.execution.command.DataWritingCommandExec.executeCollect(commands.scala:125)

	2025-08-09 16:33:32,756 - INFO - ---> 	at org.apache.spark.sql.execution.adaptive.AdaptiveSparkPlanExec.$anonfun$executeCollect$1(AdaptiveSparkPlanExec.scala:360)

	2025-08-09 16:33:32,756 - INFO - ---> 	at org.apache.spark.sql.execution.adaptive.AdaptiveSparkPlanExec.withFinalPlanUpdate(AdaptiveSparkPlanExec.scala:388)

	2025-08-09 16:33:32,756 - INFO - ---> 	at org.apache.spark.sql.execution.adaptive.AdaptiveSparkPlanExec.executeCollect(AdaptiveSparkPlanExec.scala:360)

	2025-08-09 16:33:32,756 - INFO - ---> 	at org.apache.spark.sql.execution.QueryExecution$$anonfun$eagerlyExecuteCommands$1.$anonfun$applyOrElse$1(QueryExecution.scala:98)

	2025-08-09 16:33:32,756 - INFO - ---> 	at org.apache.spark.sql.execution.SQLExecution$.$anonfun$withNewExecutionId$6(SQLExecution.scala:118)

	2025-08-09 16:33:32,757 - INFO - ---> 	at org.apache.spark.sql.execution.SQLExecution$.withSQLConfPropagated(SQLExecution.scala:195)

	2025-08-09 16:33:32,757 - INFO - ---> 	at org.apache.spark.sql.execution.SQLExecution$.$anonfun$withNewExecutionId$1(SQLExecution.scala:103)

	2025-08-09 16:33:32,757 - INFO - ---> 	at org.apache.spark.sql.SparkSession.withActive(SparkSession.scala:827)

	2025-08-09 16:33:32,757 - INFO - ---> 	at org.apache.spark.sql.execution.SQLExecution$.withNewExecutionId(SQLExecution.scala:65)

	2025-08-09 16:33:32,757 - INFO - ---> 	at org.apache.spark.sql.execution.QueryExecution$$anonfun$eagerlyExecuteCommands$1.applyOrElse(QueryExecution.scala:98)

	2025-08-09 16:33:32,757 - INFO - ---> 	at org.apache.spark.sql.execution.QueryExecution$$anonfun$eagerlyExecuteCommands$1.applyOrElse(QueryExecution.scala:94)

	2025-08-09 16:33:32,757 - INFO - ---> 	at org.apache.spark.sql.catalyst.trees.TreeNode.$anonfun$transformDownWithPruning$1(TreeNode.scala:512)

	2025-08-09 16:33:32,757 - INFO - ---> 	at org.apache.spark.sql.catalyst.trees.CurrentOrigin$.withOrigin(TreeNode.scala:104)

	2025-08-09 16:33:32,757 - INFO - ---> 	at org.apache.spark.sql.catalyst.trees.TreeNode.transformDownWithPruning(TreeNode.scala:512)

	2025-08-09 16:33:32,758 - INFO - ---> 	at org.apache.spark.sql.catalyst.plans.logical.LogicalPlan.org$apache$spark$sql$catalyst$plans$logical$AnalysisHelper$$super$transformDownWithPruning(LogicalPlan.scala:31)

	2025-08-09 16:33:32,758 - INFO - ---> 	at org.apache.spark.sql.catalyst.plans.logical.AnalysisHelper.transformDownWithPruning(AnalysisHelper.scala:267)

	2025-08-09 16:33:32,758 - INFO - ---> 	at org.apache.spark.sql.catalyst.plans.logical.AnalysisHelper.transformDownWithPruning$(AnalysisHelper.scala:263)

	2025-08-09 16:33:32,758 - INFO - ---> 	at org.apache.spark.sql.catalyst.plans.logical.LogicalPlan.transformDownWithPruning(LogicalPlan.scala:31)

	2025-08-09 16:33:32,758 - INFO - ---> 	at org.apache.spark.sql.catalyst.plans.logical.LogicalPlan.transformDownWithPruning(LogicalPlan.scala:31)

	2025-08-09 16:33:32,758 - INFO - ---> 	at org.apache.spark.sql.catalyst.trees.TreeNode.transformDown(TreeNode.scala:488)

	2025-08-09 16:33:32,758 - INFO - ---> 	at org.apache.spark.sql.execution.QueryExecution.eagerlyExecuteCommands(QueryExecution.scala:94)

	2025-08-09 16:33:32,758 - INFO - ---> 	at org.apache.spark.sql.execution.QueryExecution.commandExecuted$lzycompute(QueryExecution.scala:81)

	2025-08-09 16:33:32,758 - INFO - ---> 	at org.apache.spark.sql.execution.QueryExecution.commandExecuted(QueryExecution.scala:79)

	2025-08-09 16:33:32,759 - INFO - ---> 	at org.apache.spark.sql.Dataset.<init>(Dataset.scala:218)

	2025-08-09 16:33:32,759 - INFO - ---> 	at org.apache.spark.sql.Dataset$.$anonfun$ofRows$2(Dataset.scala:98)

	2025-08-09 16:33:32,759 - INFO - ---> 	at org.apache.spark.sql.SparkSession.withActive(SparkSession.scala:827)

	2025-08-09 16:33:32,759 - INFO - ---> 	at org.apache.spark.sql.Dataset$.ofRows(Dataset.scala:95)

	2025-08-09 16:33:32,759 - INFO - ---> 	at org.apache.spark.sql.SparkSession.$anonfun$sql$1(SparkSession.scala:640)

	2025-08-09 16:33:32,759 - INFO - ---> 	at org.apache.spark.sql.SparkSession.withActive(SparkSession.scala:827)

	2025-08-09 16:33:32,759 - INFO - ---> 	at org.apache.spark.sql.SparkSession.sql(SparkSession.scala:630)

	2025-08-09 16:33:32,759 - INFO - ---> 	at org.apache.spark.sql.SparkSession.sql(SparkSession.scala:671)

	2025-08-09 16:33:32,759 - INFO - ---> 	at com.spark.main.SparkSql.main(SparkSql.java:110)

	2025-08-09 16:33:32,759 - INFO - ---> 	at sun.reflect.NativeMethodAccessorImpl.invoke0(Native Method)

	2025-08-09 16:33:32,760 - INFO - ---> 	at sun.reflect.NativeMethodAccessorImpl.invoke(NativeMethodAccessorImpl.java:62)

	2025-08-09 16:33:32,760 - INFO - ---> 	at sun.reflect.DelegatingMethodAccessorImpl.invoke(DelegatingMethodAccessorImpl.java:43)

	2025-08-09 16:33:32,760 - INFO - ---> 	at java.lang.reflect.Method.invoke(Method.java:498)

	2025-08-09 16:33:32,760 - INFO - ---> 	at org.apache.spark.deploy.yarn.ApplicationMaster$$anon$2.run(ApplicationMaster.scala:757)

	2025-08-09 16:33:32,760 - INFO - ---> 

	2025-08-09 16:33:32,760 - INFO - ---> Exception in thread "main" org.apache.spark.SparkException: Application application_1752723821416_15504 finished with failed status

	2025-08-09 16:33:32,760 - INFO - ---> 	at org.apache.spark.deploy.yarn.Client.run(Client.scala:1325)

	2025-08-09 16:33:32,760 - INFO - ---> 	at org.apache.spark.deploy.yarn.YarnClusterApplication.start(Client.scala:1758)

	2025-08-09 16:33:32,761 - INFO - ---> 	at org.apache.spark.deploy.SparkSubmit.org$apache$spark$deploy$SparkSubmit$$runMain(SparkSubmit.scala:1020)

	2025-08-09 16:33:32,761 - INFO - ---> 	at org.apache.spark.deploy.SparkSubmit.doRunMain$1(SparkSubmit.scala:192)

	2025-08-09 16:33:32,761 - INFO - ---> 	at org.apache.spark.deploy.SparkSubmit.submit(SparkSubmit.scala:215)

	2025-08-09 16:33:32,761 - INFO - ---> 	at org.apache.spark.deploy.SparkSubmit.doSubmit(SparkSubmit.scala:91)

	2025-08-09 16:33:32,761 - INFO - ---> 	at org.apache.spark.deploy.SparkSubmit$$anon$2.doSubmit(SparkSubmit.scala:1111)

	2025-08-09 16:33:32,761 - INFO - ---> 	at org.apache.spark.deploy.SparkSubmit$.main(SparkSubmit.scala:1120)

	2025-08-09 16:33:32,761 - INFO - ---> 	at org.apache.spark.deploy.SparkSubmit.main(SparkSubmit.scala)

	2025-08-09 16:33:32,761 - INFO - ---> 25/08/09 16:33:32 INFO ShutdownHookManager: Shutdown hook called

	2025-08-09 16:33:32,762 - INFO - ---> 25/08/09 16:33:32 INFO ShutdownHookManager: Deleting directory /tmp/spark-b31ef5fc-7404-4282-b0ca-08675cd406c9

	2025-08-09 16:33:32,767 - INFO - ---> 25/08/09 16:33:32 INFO ShutdownHookManager: Deleting directory /tmp/spark-7567ee38-cec2-4ce1-b727-5ba22eb38531

[INFO] 2025-08-09 16:33:33.909 +0800 -  -> 

	2025-08-09 16:33:33,169 - INFO - 返回码：1

	2025-08-09 16:33:33,169 - ERROR - 命令执行失败

	2025-08-09 16:33:33,169 - INFO - spark任务执行结果：False

	2025-08-09 16:33:33,169 - ERROR - sql文件：/data/dataware/batchdatawarehouse/spark/ads/ads_bi_ad_short_cost_info_1d.sql，sql命令执行失败，程序退出

[INFO] 2025-08-09 16:33:33.910 +0800 - process has exited. execute path:/tmp/dolphinscheduler/exec/process/default/18495586806848/18554149119168_7/26526/74520, processId:195467 ,exitStatusCode:1 ,processWaitForStatus:true ,processExitValue:1

[INFO] 2025-08-09 16:33:33.912 +0800 - ***********************************************************************************************

[INFO] 2025-08-09 16:33:33.912 +0800 - *********************************  Finalize task instance  ************************************

[INFO] 2025-08-09 16:33:33.912 +0800 - ***********************************************************************************************

[INFO] 2025-08-09 16:33:33.912 +0800 - Upload output files: [] successfully

[INFO] 2025-08-09 16:33:33.913 +0800 - Send task execute status: FAILURE to master : *********:1234

[INFO] 2025-08-09 16:33:33.914 +0800 - Remove the current task execute context from worker cache

[INFO] 2025-08-09 16:33:33.914 +0800 - The current execute mode isn't develop mode, will clear the task execute file: /tmp/dolphinscheduler/exec/process/default/18495586806848/18554149119168_7/26526/74520

[INFO] 2025-08-09 16:33:33.914 +0800 - Success clear the task execute file: /tmp/dolphinscheduler/exec/process/default/18495586806848/18554149119168_7/26526/74520
