import logging
import os
import sys
import time
from datetime import datetime,timezone, timedelta, date
from public_init import *

formatter = logging.Formatter('%(asctime)s - %(levelname)s - %(message)s')
logger = logging.getLogger("my_logger")
logger.setLevel(logging.DEBUG)

def initlog(table_name):

    streamHander = logging.StreamHandler(sys.stdout)
    streamHander.setFormatter(formatter)
    logger.addHandler(streamHander)

    execution_day = (datetime.now(timezone.utc) + timedelta(hours=8) ).strftime('%Y%m%d')

    log_file = f"{CLUSTER_PATH}/logs/{execution_day}/{table_name.strip().lower()}_{time.time()}.log"

    log_path = os.path.dirname(log_file)
    if not os.path.exists(log_path):
        try:
            os.makedirs(log_path)
        except Exception as e:
            logger.info(e)

    print("************LOG*************\n")
    print(log_file)
    print("\n**********LOG**************")

    handler = logging.FileHandler(log_file)
    handler.setFormatter(formatter)
    logger.addHandler(handler)
